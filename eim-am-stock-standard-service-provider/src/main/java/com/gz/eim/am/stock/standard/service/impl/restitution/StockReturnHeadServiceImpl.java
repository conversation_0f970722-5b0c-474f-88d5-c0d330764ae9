package com.gz.eim.am.stock.standard.service.impl.restitution;


import com.fuu.eim.support.util.DateUtils;
import com.gz.eim.am.stock.core.constant.CommonConstant;
import com.gz.eim.am.stock.standard.dao.restitution.StockReturnHeadMapper;
import com.gz.eim.am.stock.standard.dto.request.restitution.GoodsReturnHeadSearchReqDTO;
import com.gz.eim.am.stock.standard.entity.restitution.StockReturnHead;
import com.gz.eim.am.stock.standard.entity.restitution.StockReturnHeadExample;
import com.gz.eim.am.stock.standard.enums.InventoryInPlanHeadEnum;
import com.gz.eim.am.stock.standard.service.restitution.StockReturnHeadService;
import com.gz.eim.am.stock.standard.util.common.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class StockReturnHeadServiceImpl implements StockReturnHeadService {

    @Resource
    private StockReturnHeadMapper stockReturnHeadMapper;

    @Override
    public int insertStockReturnHead(StockReturnHead stockReturnHead) {
        if (null == stockReturnHead){
            return CommonConstant.NUMBER_ZERO;
        }
        return stockReturnHeadMapper.insertSelective(stockReturnHead);
    }

    @Override
    public List<StockReturnHead> listStockReturnHead(StockReturnHead stockReturnHead) {
        if (Objects.isNull(stockReturnHead)){
            log.info("StockReturnHeadServiceImpl.listStockReturnHead param is null");
            return Collections.emptyList();
        }
        StockReturnHeadExample example = new StockReturnHeadExample();
        StockReturnHeadExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(stockReturnHead.getReturnNo())){
            criteria.andReturnNoEqualTo(stockReturnHead.getReturnNo());
        }
        if (Objects.nonNull(stockReturnHead.getId())){
            criteria.andIdEqualTo(stockReturnHead.getId());
        }
        return stockReturnHeadMapper.selectByExample(example);
    }

    @Override
    public int updateStockReturnHead(StockReturnHead stockReturnHead) {
        if (Objects.isNull(stockReturnHead)){
            log.info("StockReturnHeadServiceImpl.updateStockReturnHead param is null");
            return CommonConstant.NUMBER_ZERO;
        }
        return stockReturnHeadMapper.updateByPrimaryKeySelective(stockReturnHead);
    }

    @Override
    public List<StockReturnHead> listStockReturnHeadBySearch(GoodsReturnHeadSearchReqDTO searchReqDTO) throws ParseException {
        //if (Objects.isNull(searchReqDTO)){
        //    log.info("StockReturnHeadServiceImpl.listStockReturnHeadBySearch param is null");
        //    return Collections.emptyList();
        //}
        StockReturnHeadExample example = new StockReturnHeadExample();
        StockReturnHeadExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(searchReqDTO.getReturnNo())){
            criteria.andReturnNoEqualTo(searchReqDTO.getReturnNo());
        }
        // 业务单号
        if (StringUtils.isNotBlank(searchReqDTO.getBizNo())){
            criteria.andBizNoEqualTo(searchReqDTO.getBizNo());
        }
        // 审批状态
        if (Objects.nonNull(searchReqDTO.getStatus())){
            criteria.andStatusEqualTo(searchReqDTO.getStatus());
        }
        if (Objects.nonNull(searchReqDTO.getReturnStatus())){
            criteria.andReturnStatusEqualTo(searchReqDTO.getReturnStatus());
        }
        if (StringUtils.isNotBlank(searchReqDTO.getDutyUser())){
            criteria.andDutyUserEqualTo(searchReqDTO.getDutyUser());
        }
        if (StringUtils.isNotBlank(searchReqDTO.getBillingUser())){
            criteria.andBillingUserEqualTo(searchReqDTO.getBillingUser());
        }
        if (StringUtils.isNotBlank(searchReqDTO.getWarehouseCode())){
            criteria.andWarehouseCodeEqualTo(searchReqDTO.getWarehouseCode());
        }else {
            if (CollectionUtils.isNotEmpty(searchReqDTO.getPermissionWarehouseList())){
                criteria.andWarehouseCodeIn(searchReqDTO.getPermissionWarehouseList());
            }
        }
        if (StringUtils.isNoneBlank(searchReqDTO.getBeginBillingTimeStr(), searchReqDTO.getEndBillingTimeStr())){
            criteria.andBillingTimeBetween(DateUtils.dateParse(searchReqDTO.getBeginBillingTimeStr(), null), DateTimeUtil.getDayEnd(DateUtils.dateParse(searchReqDTO.getEndBillingTimeStr(), null)));
        }
        // 实际归还日期
        if (StringUtils.isNoneBlank(searchReqDTO.getBeginReallyReturnTimeStr(), searchReqDTO.getEndReallyReturnTimeStr())){
            criteria.andRellyReturnTimeBetween(DateUtils.dateParse(searchReqDTO.getBeginReallyReturnTimeStr(), null), DateTimeUtil.getDayEnd(DateUtils.dateParse(searchReqDTO.getEndReallyReturnTimeStr(), null)));
        }

        if (StringUtils.isNoneBlank(searchReqDTO.getBeginReturnTimeStr(), searchReqDTO.getEndReturnTimeStr())){
            criteria.andReturnTimeBetween(DateUtils.dateParse(searchReqDTO.getBeginReturnTimeStr(), null), DateTimeUtil.getDayEnd(DateUtils.dateParse(searchReqDTO.getEndReturnTimeStr(), null)));
        }
        if (StringUtils.isNotBlank(searchReqDTO.getCauseCode())){
            criteria.andCauseCodeEqualTo(searchReqDTO.getCauseCode());
        }
        if (StringUtils.isNotBlank(searchReqDTO.getUseTypeCode())){
            criteria.andUseTypeCodeEqualTo(searchReqDTO.getUseTypeCode());
        }
        // 是否显示已取消单据
        if (Objects.nonNull(searchReqDTO.getShowCancel()) && searchReqDTO.getShowCancel()){
            criteria.andReturnStatusEqualTo(InventoryInPlanHeadEnum.Status.CANCEL.getCode());
        } else {
            criteria.andReturnStatusNotInAndExcludeNull(Collections.singletonList(InventoryInPlanHeadEnum.Status.CANCEL.getCode()));
        }

        example.setOrderByClause("created_at desc");
        return stockReturnHeadMapper.selectByExample(example);
    }
}
