#redis??
spring.redisson.address = ************:5004
spring.redisson.password=fb9092
##??????
file.dir = /Users/<USER>/workspace/
## openoffice????
office.home = /Applications/LibreOffice.app
spring.resources.static-locations = classpath:/META-INF/resources/,classpath:/resources/,classpath:/static/,classpath:/public/,file:${file.dir}
server.tomcat.uri-encoding = UTF-8
converted.file.charset = GBK
#??????
spring.http.multipart.max-file-size=100MB
spring.http.multipart.max-request-size=100MB
##????
simText = txt,html,xml,java,properties,sql
#?????
media=mp3,mp4,flv,rmvb

########################################################
###PageOffice
########################################################
posyspath=/Users/<USER>/workspace/
popassword=111111
