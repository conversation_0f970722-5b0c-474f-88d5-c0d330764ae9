;--------------------------------------------------------------------------------
; winPenPack Project � X-Software collection
; Copyright � 2005-2013 <PERSON><PERSON> and winPenPack Development Team
; X-Launcher 1.5.1 Copyright � <PERSON><PERSON> and winPenPack Development Team
;
; Web: http://www.winpenpack.com/
; E-mail: <EMAIL>
;
; Released under the terms and conditions of the winPenPack License Agreement 
; (see license.txt or http://www.winpenpack.com/main/page.php?5)
;--------------------------------------------------------------------------------
[Version]
Ini Author=<PERSON><PERSON>
Ini Revision=3
Ini Rev.Author=
Ini Date=23/07/2013
Ini Locale=it
Software=Apache OpenOffice
Soft.Author=The Apache Software Foundation
Soft.Version=4.0.0
Web=http://www.openoffice.org/
Launcher=1.5.1
System=

[Group]
Category=
Sub Category=
License=Apache License 2.0
;--------------------------------------------------------------------------------

[Setup]
AppName=OpenOffice 4
AppVer=4.0.0

[FileToRun]
PathToExe=$Bin$\$AppName$\program\soffice.exe

[Environment]
OPEN_OFFICE_USER_HOME=$Home$\$AppName$
DOC_DIR=$Doc$
DOWNLOAD_DIR=$Download$
BACKUP_DIR=$Backup$\$AppName$
PATH=$Lib$\Java

[Functions]
DirCreate=%OPEN_OFFICE_USER_HOME%|%DOC_DIR%|%DOWNLOAD_DIR%|%BACKUP_DIR%
FileCopy=$Temp$\x-default|%OPEN_OFFICE_USER_HOME%\user\registrymodifications.xcu
FileMove=$ExeDir$\quickstart.exe|$ExeDir$\quickstart.exe.$AppVer$
FileCopy=$Lib$\$AppName$\fonts\opens___.ttf|$Bin$\$AppName$\share\fonts\truetype\opens___.ttf

[WriteToIni=$ExeDir$\bootstrap.ini]
Bootstrap|UserInstallation=file:///%OPEN_OFFICE_USER_HOME%|%20/

[StringRegExpReplace=%OPEN_OFFICE_USER_HOME%\user\registrymodifications.xcu]
<<RE>>|0|%20/=(file:///)([A-Za-z]:/)[^"<>:=\n\r]+(/(User|Bin|Documents|Downloads|Backups)[/<])<<RE>>$1{$Root$}$3
<<RE>>|0|%20/=(file:///)([A-Za-z]:/)[^"<>:=\n\r]+(/Temp/X-OpenOffice)<<RE>>$1{$LocalSettings$}$3
<<RE>>|0=(<value>)([A-Za-z]:\\)[^"<>:=\n\r]+(\\(User|Bin|Documents|Downloads|Backups)\\)<<RE>>$1{$Root$}$3

[RunAfter]
DirRemove=$Lib$\$AppName$
FileDelete=%OPEN_OFFICE_USER_HOME%\user\config\javasettings_Windows_x86.xml

[Options]
Java=true
DeleteTemp=true
RunWait=true
ShowSplash=false
WriteLog=false
ShowTrayTip=true