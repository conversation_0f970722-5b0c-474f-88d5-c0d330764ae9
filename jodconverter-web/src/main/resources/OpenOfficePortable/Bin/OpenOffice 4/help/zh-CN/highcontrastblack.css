/**************************************************************
 * 
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 * 
 *   http://www.apache.org/licenses/LICENSE-2.0
 * 
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 * 
 *************************************************************/

/*
+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
+                          OPENOFFICE.ORG 2.0 HELP                  +
+                      HIGH CONTRAST BLACK STYLESHEET               +
+                           CHINESE SIMPL                           +
+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
+ LAST CHANGES: 24-MAR-2005                                         +
+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
*/


body, p, h1, h2, h3, h4, h5, h6, .listitem, .listitemintable, .tablecontent, .tablecontentintable  
	{ font-family:  方正宋体,"MSung Light SC",SimSun,FZSongYi,FZShuSong,NSimSun,"Andale Sans UI","Arial Unicode MS","Lucida Sans Unicode",Song; }
	
.code, .codeintable, .example, .exampleintable, .literal, .literalintable, .path, .pathintable
	{ font-family: 方正宋体,"MSung Light SC",Cumberland,Courier New,Courier,"Lucida Sans Typewriter","Lucida Typewriter",Monaco,Monospaced; margin-top: 1pt; margin-bottom: 1pt;}

.acronym 
	{ font-weight: bold; }

.related
	{ font-weight: bold; margin-top:20pt; border-top: 1px solid black;}

.emph, .menuitem, .keycode
	{ font-weight: bold; }

.tablehead, .tableheadintable
	{ font-weight: bold; margin-top: 0px;}

.howtogetheader
	{ font-weight: bold; padding: 3px;}

h1, h2, h3, h4, h5, h6
	{ margin-bottom: 5pt; }

p, td
	{ font-size: 10pt; }

h1
	{ font-size: 18pt; border-bottom: 1px solid black; padding-bottom: 6px; margin-bottom: 6px;}

h2
	{ font-size: 14pt; }

h3
	{ font-size: 12pt; }

h4, h5, h6
	{ font-size: 10pt; }

.relatedtopics
	{ font-weight: normal; }

.relatedbody  
	{ margin-top: 2px; margin-bottom: 2px; margin-left: 5px; }

.wide 
	{ width: 100%; }

.topalign
	 { vertical-align: top; border: 1px;}

.bug 
	{ color: red; border: 1px solid red;}

.debug
	{ border: 1px solid black; padding: 3px; display: none;}

/* HIGH CONTRAST SPECIFIC SETTINGS */

body, p, h1, h2, h3, h4, h5, h6, .listitem, .listitemintable, .tablecontent, .tablecontentintable
	{ background: #000000; color: #FFFFFF;}

.related 
	{ border-top: 1px solid #FFFFFF; }

.howtogetheader
	{border: 1px solid #FFFFFF; background: #000000;}

h1 
	{ border-bottom: 1px solid #FFFFFF; ]

.howtoget
	{ background:#000000;}

.howtogetbody
	{ background:#000000;}

.debug
	{ border: 1px solid red; padding: 3px; display: none;}

a
 { color: #FFFFFF; }
