<?xml version="1.0" encoding="UTF-8"?>
<!--***********************************************************
 * 
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 * 
 *   http://www.apache.org/licenses/LICENSE-2.0
 * 
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 * 
 ***********************************************************-->
<tree_view version="24-Aug-2004">
	<help_section application="simpress" id="04" title="演示文稿和绘图">
		<node id="0401" title="一般信息和用户界面的使用">
<topic id="simpress/text/simpress/main0000.xhp">欢迎使用 %PRODUCTNAME Impress 帮助</topic>
<topic id="sdraw/text/sdraw/main0000.xhp">欢迎使用 %PRODUCTNAME Draw 帮助</topic>
<topic id="simpress/text/simpress/main0503.xhp">%PRODUCTNAME Impress 功能</topic>
<topic id="sdraw/text/sdraw/main0503.xhp">%PRODUCTNAME Draw 功能</topic>
<topic id="simpress/text/simpress/guide/keyboard.xhp">使用 %PRODUCTNAME Impress 中的快捷键</topic>
<topic id="sdraw/text/sdraw/guide/keyboard.xhp">绘图对象的快捷键</topic>
<topic id="simpress/text/simpress/04/01020000.xhp">%PRODUCTNAME Impress 的快捷键</topic>
<topic id="sdraw/text/sdraw/04/01020000.xhp">绘图快捷键</topic>
<topic id="simpress/text/simpress/guide/main.xhp">%PRODUCTNAME Impress 使用说明</topic>
<topic id="sdraw/text/sdraw/guide/main.xhp">使用 %PRODUCTNAME Draw 的说明</topic>
		</node>
		<node id="0402" title="命令和菜单引用">
			<node id="040201" title="演示文稿 (%PRODUCTNAME Impress)">
				<node id="04020101" title="菜单">
<topic id="simpress/text/simpress/main0100.xhp">菜单</topic>
<topic id="simpress/text/simpress/main0101.xhp">文件</topic>
<topic id="simpress/text/simpress/main0102.xhp">编辑</topic>
<topic id="simpress/text/simpress/main0103.xhp">视图</topic>
<topic id="simpress/text/simpress/main0104.xhp">插入</topic>
<topic id="simpress/text/simpress/main0105.xhp">格式</topic>
<topic id="simpress/text/simpress/main0106.xhp">工具</topic>
<topic id="simpress/text/simpress/main0107.xhp">窗口</topic>
<topic id="simpress/text/simpress/main0114.xhp">幻灯片放映</topic>
				</node>
				<node id="04020102" title="工具栏">
<topic id="simpress/text/simpress/main0200.xhp">工具栏</topic>
<topic id="simpress/text/simpress/main0202.xhp">线条和填充栏</topic>
<topic id="simpress/text/simpress/main0203.xhp">文字格式栏</topic>
<topic id="simpress/text/simpress/main0204.xhp">幻灯片视图中的对象栏</topic>
<topic id="simpress/text/simpress/main0206.xhp">状态栏</topic>
<topic id="simpress/text/simpress/main0209.xhp">标尺</topic>
<topic id="simpress/text/simpress/main0210.xhp">绘图栏</topic>
<topic id="simpress/text/simpress/main0211.xhp">大纲工具栏</topic>
<topic id="simpress/text/simpress/main0212.xhp">幻灯片浏览栏</topic>
<topic id="simpress/text/simpress/main0213.xhp">选项栏</topic>
<topic id="simpress/text/simpress/main0214.xhp">图片栏</topic>
<topic id="shared/text/shared/main0201.xhp">标准栏</topic>
<topic id="shared/text/shared/main0209.xhp">超链接地址栏</topic>
<topic id="shared/text/shared/main0213.xhp">窗体导航栏</topic>
<topic id="shared/text/shared/main0226.xhp">窗体设计工具栏</topic>
<topic id="shared/text/shared/main0227.xhp">编辑接点栏</topic>
				</node>
			</node>
			<node id="040202" title="绘图 (%PRODUCTNAME Draw)">
				<node id="04020201" title="菜单">
<topic id="sdraw/text/sdraw/main0100.xhp">菜单</topic>
<topic id="sdraw/text/sdraw/main0101.xhp">文件</topic>
<topic id="sdraw/text/sdraw/main0102.xhp">编辑</topic>
<topic id="sdraw/text/sdraw/main0103.xhp">视图</topic>
<topic id="sdraw/text/sdraw/main0104.xhp">插入</topic>
<topic id="sdraw/text/sdraw/main0105.xhp">格式</topic>
<topic id="sdraw/text/sdraw/main0106.xhp">工具</topic>
<topic id="simpress/text/simpress/main0113.xhp">修改</topic>
				</node>
				<node id="04020202" title="工具栏">
<topic id="sdraw/text/sdraw/main0200.xhp">工具栏</topic>
<topic id="sdraw/text/sdraw/main0210.xhp">绘图栏</topic>
<topic id="sdraw/text/sdraw/main0213.xhp">选项工具栏</topic>
<topic id="shared/text/shared/main0201.xhp">标准栏</topic>
<topic id="shared/text/shared/main0209.xhp">超链接地址栏</topic>
<topic id="shared/text/shared/main0213.xhp">窗体导航栏</topic>
<topic id="shared/text/shared/main0226.xhp">窗体设计工具栏</topic>
<topic id="shared/text/shared/main0227.xhp">编辑接点栏</topic>
				</node>
			</node>
		</node>
		<node id="0403" title="加载、保存、导入和导出">
<topic id="simpress/text/simpress/guide/html_export.xhp">以 HTML 格式保存演示文稿</topic>
<topic id="simpress/text/simpress/guide/html_import.xhp">将 HTML 页面导入演示文稿</topic>
<topic id="simpress/text/simpress/guide/palette_files.xhp">加载颜色、渐变和阴影线列表。</topic>
<topic id="simpress/text/simpress/guide/animated_gif_save.xhp">以 GIF 格式导出动画</topic>
<topic id="simpress/text/simpress/guide/table_insert.xhp">在幻灯片中包括电子表格</topic>
<topic id="sdraw/text/sdraw/guide/graphic_insert.xhp">插入图形</topic>
<topic id="simpress/text/simpress/guide/page_copy.xhp">从其他演示文稿中复制幻灯片</topic>
		</node>
		<node id="0404" title="格式化">
<topic id="simpress/text/simpress/guide/palette_files.xhp">加载颜色、渐变和阴影线列表。</topic>
<topic id="simpress/text/simpress/guide/line_arrow_styles.xhp">加载线条和箭头样式</topic>
<topic id="sdraw/text/sdraw/guide/color_define.xhp">定义自定义颜色</topic>
<topic id="sdraw/text/sdraw/guide/gradient.xhp">创建渐变填充</topic>
<topic id="sdraw/text/sdraw/guide/eyedropper.xhp">替换颜色</topic>
<topic id="sdraw/text/sdraw/guide/align_arrange.xhp">排列，对齐和分布对象</topic>
<topic id="simpress/text/simpress/guide/background.xhp">修改幻灯片背景填充</topic>
<topic id="simpress/text/simpress/guide/footer.xhp">向所有幻灯片添加页眉或页脚</topic>
<topic id="simpress/text/simpress/guide/masterpage.xhp">将幻灯片设计应用于幻灯片母版</topic>
<topic id="simpress/text/simpress/guide/move_object.xhp">移动对象</topic>
		</node>
		<node id="0405" title="打印">
<topic id="simpress/text/simpress/guide/printing.xhp">打印演示文稿</topic>
<topic id="simpress/text/simpress/guide/print_tofit.xhp">根据纸张尺寸调整幻灯片打印</topic>
		</node>
		<node id="0406" title="效果">
<topic id="simpress/text/simpress/guide/animated_gif_save.xhp">以 GIF 格式导出动画</topic>
<topic id="simpress/text/simpress/guide/animated_objects.xhp">为演示文稿幻灯片中的对象设置动画</topic>
<topic id="simpress/text/simpress/guide/animated_slidechange.xhp">以动画方式切换幻灯片</topic>
<topic id="sdraw/text/sdraw/guide/cross_fading.xhp">交叉淡入淡出两个对象</topic>
<topic id="simpress/text/simpress/guide/animated_gif_create.xhp">创建动画 GIF 图像</topic>
		</node>
		<node id="0407" title="对象、图形和位图">
<topic id="sdraw/text/sdraw/guide/combine_etc.xhp">组合对象和构造形状</topic>
<topic id="sdraw/text/sdraw/guide/draw_sector.xhp">绘制扇形和圆缺</topic>
<topic id="sdraw/text/sdraw/guide/duplicate_object.xhp">复制对象</topic>
<topic id="sdraw/text/sdraw/guide/rotate_object.xhp">旋转对象</topic>
<topic id="sdraw/text/sdraw/guide/join_objects3d.xhp">组合三维对象</topic>
<topic id="sdraw/text/sdraw/guide/join_objects.xhp">连接线条</topic>
<topic id="simpress/text/simpress/guide/text2curve.xhp">将文本字符转换为绘图对象</topic>
<topic id="simpress/text/simpress/guide/vectorize.xhp">将位图图像转换成矢量图形</topic>
<topic id="simpress/text/simpress/guide/3d_create.xhp">将二维对象转换成曲线、多边形和三维对象</topic>
<topic id="simpress/text/simpress/guide/line_arrow_styles.xhp">加载线条和箭头样式</topic>
<topic id="simpress/text/simpress/guide/line_draw.xhp">绘制曲线</topic>
<topic id="simpress/text/simpress/guide/line_edit.xhp">编辑曲线</topic>
<topic id="sdraw/text/sdraw/guide/graphic_insert.xhp">插入图形</topic>
<topic id="simpress/text/simpress/guide/table_insert.xhp">在幻灯片中包括电子表格</topic>
<topic id="simpress/text/simpress/guide/move_object.xhp">移动对象</topic>
<topic id="simpress/text/simpress/guide/select_object.xhp">选择底层对象</topic>
<topic id="simpress/text/simpress/guide/orgchart.xhp">创建流程图</topic>
		</node>
		<node id="0408" title="组合和分层">
<topic id="sdraw/text/sdraw/guide/groups.xhp">组合对象</topic>
<topic id="simpress/text/simpress/guide/layers.xhp">关于层</topic>
<topic id="simpress/text/simpress/guide/layer_new.xhp">插入层</topic>
<topic id="simpress/text/simpress/guide/layer_tipps.xhp">使用层</topic>
<topic id="simpress/text/simpress/guide/layer_move.xhp">将对象移至另一层</topic>
		</node>
		<node id="0409" title="演示文稿和绘图中的文字">
<!-- removed sdraw/text/sdraw/guide/text_3d.xhp -->
<!-- removed sdraw/text/sdraw/guide/text_to_bitmap.xhp -->
<topic id="sdraw/text/sdraw/guide/text_enter.xhp">添加文本</topic>
<topic id="simpress/text/simpress/guide/text2curve.xhp">将文本字符转换为绘图对象</topic>
		</node>
		<node id="0410" title="查看">
<topic id="simpress/text/simpress/guide/individual.xhp">创建自定义幻灯片放映</topic>
<topic id="simpress/text/simpress/guide/arrange_slides.xhp">修改幻灯片顺序</topic>
<topic id="simpress/text/simpress/guide/change_scale.xhp">使用小键盘缩放</topic>
<!-- removed simpress/text/simpress/guide/livemode.xhp -->
<topic id="simpress/text/simpress/guide/rehearse_timings.xhp">幻灯片更换的排练计时</topic>
		</node>
	</help_section>
</tree_view>
