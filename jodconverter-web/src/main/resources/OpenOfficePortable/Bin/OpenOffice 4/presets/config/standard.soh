<?xml version="1.0" encoding="UTF-8"?>

<office:hatch-table xmlns:office="http://openoffice.org/2000/office" xmlns:style="http://openoffice.org/2000/style" xmlns:text="http://openoffice.org/2000/text" xmlns:table="http://openoffice.org/2000/table" xmlns:draw="http://openoffice.org/2000/drawing" xmlns:fo="http://www.w3.org/1999/XSL/Format" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:meta="http://openoffice.org/2000/meta" xmlns:number="http://openoffice.org/2000/datastyle" xmlns:svg="http://www.w3.org/2000/svg" xmlns:chart="http://openoffice.org/2000/chart" xmlns:dr3d="http://openoffice.org/2000/dr3d" xmlns:math="http://www.w3.org/1998/Math/MathML" xmlns:form="http://openoffice.org/2000/form" xmlns:script="http://openoffice.org/2000/script">
 <draw:hatch draw:name="Black 0 Degrees" draw:style="single" draw:color="#000000" draw:distance="0.0402inch" draw:rotation="0"/>
 <draw:hatch draw:name="Black 45 Degrees" draw:style="single" draw:color="#000000" draw:distance="0.0402inch" draw:rotation="450"/>
 <draw:hatch draw:name="Black -45 Degrees" draw:style="single" draw:color="#000000" draw:distance="0.0402inch" draw:rotation="3150"/>
 <draw:hatch draw:name="Black 90 Degrees" draw:style="single" draw:color="#000000" draw:distance="0.0402inch" draw:rotation="900"/>
 <draw:hatch draw:name="Red Crossed 45 Degrees" draw:style="double" draw:color="#800000" draw:distance="0.0299inch" draw:rotation="450"/>
 <draw:hatch draw:name="Red Crossed 0 Degrees" draw:style="double" draw:color="#800000" draw:distance="0.0299inch" draw:rotation="900"/>
 <draw:hatch draw:name="Blue Crossed 45 Degrees" draw:style="double" draw:color="#000080" draw:distance="0.0299inch" draw:rotation="450"/>
 <draw:hatch draw:name="Blue Crossed 0 Degrees" draw:style="double" draw:color="#000080" draw:distance="0.0299inch" draw:rotation="900"/>
 <draw:hatch draw:name="Blue Triple 90 Degrees" draw:style="triple" draw:color="#0000ff" draw:distance="0.0402inch" draw:rotation="900"/>
 <draw:hatch draw:name="Black 45 Degrees Wide" draw:style="single" draw:color="#000000" draw:distance="0.2inch" draw:rotation="450"/>
</office:hatch-table>