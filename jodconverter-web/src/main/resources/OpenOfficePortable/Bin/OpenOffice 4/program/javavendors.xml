<?xml version="1.0" encoding="UTF-8"?>
<!--***********************************************************
 * 
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 * 
 *   http://www.apache.org/licenses/LICENSE-2.0
 * 
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 * 
 ***********************************************************-->

<javaSelection xmlns="http://openoffice.org/2004/java/framework/1.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

 <updated>2012-10-06</updated>

 <vendorInfos>
  <vendor name="Oracle Corporation">
    <minVersion>1.6.0</minVersion>
  </vendor>
  <vendor name="Sun Microsystems Inc.">
    <minVersion>1.5.0</minVersion>
  </vendor>
  <vendor name="IBM Corporation">
    <minVersion>1.5.0</minVersion>
  </vendor>
 </vendorInfos>

 <plugins>
  <library vendor="Oracle Corporation">vnd.sun.star.expand:$URE_INTERNAL_LIB_DIR/sunjavaplugin.dll</library>
  <library vendor="Sun Microsystems Inc.">vnd.sun.star.expand:$URE_INTERNAL_LIB_DIR/sunjavaplugin.dll</library>
  <library vendor="IBM Corporation">vnd.sun.star.expand:$URE_INTERNAL_LIB_DIR/sunjavaplugin.dll</library>
 </plugins>
</javaSelection>
