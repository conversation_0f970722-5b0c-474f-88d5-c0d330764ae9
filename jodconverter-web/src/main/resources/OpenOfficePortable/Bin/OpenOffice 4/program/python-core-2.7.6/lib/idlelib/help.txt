[See the end of this file for ** TIPS ** on using IDLE !!]

Click on the dotted line at the top of a menu to "tear it off": a
separate window containing the menu is created.

File Menu:

	New File         -- Create a new editing window
	Open...          -- Open an existing file
	Recent Files...  -- Open a list of recent files
	Open Module...   -- Open an existing module (searches sys.path)
	Class Browser    -- Show classes and methods in current file
	Path Browser     -- Show sys.path directories, modules, classes
                            and methods
	---
	Save             -- Save current window to the associated file (unsaved
		            windows have a * before and after the window title)

	Save As...       -- Save current window to new file, which becomes
		            the associated file
	Save Copy As...  -- Save current window to different file
		            without changing the associated file
	---
	Print Window     -- Print the current window
	---
	Close            -- Close current window (asks to save if unsaved)
	Exit             -- Close all windows, quit (asks to save if unsaved)

Edit Menu:

	Undo             -- Undo last change to current window
                            (A maximum of 1000 changes may be undone)
	Redo             -- Redo last undone change to current window
	---
	Cut              -- Copy a selection into system-wide clipboard,
                            then delete the selection
	Copy             -- Copy selection into system-wide clipboard
	Paste            -- Insert system-wide clipboard into window
	Select All       -- Select the entire contents of the edit buffer
	---
	Find...          -- Open a search dialog box with many options
	Find Again       -- Repeat last search
	Find Selection   -- Search for the string in the selection
	Find in Files... -- Open a search dialog box for searching files
	Replace...       -- Open a search-and-replace dialog box
	Go to Line       -- Ask for a line number and show that line
	Show Calltip     -- Open a small window with function param hints
	Show Completions -- Open a scroll window allowing selection keywords
			    and attributes. (see '*TIPS*', below)
	Show Parens	 -- Highlight the surrounding parenthesis
	Expand Word      -- Expand the word you have typed to match another
		            word in the same buffer; repeat to get a
                            different expansion

Format Menu (only in Edit window):

	Indent Region       -- Shift selected lines right 4 spaces
	Dedent Region       -- Shift selected lines left 4 spaces
	Comment Out Region  -- Insert ## in front of selected lines
	Uncomment Region    -- Remove leading # or ## from selected lines
	Tabify Region       -- Turns *leading* stretches of spaces into tabs
		(Note: We recommend using 4 space blocks to indent Python code.)
	Untabify Region     -- Turn *all* tabs into the right number of spaces
	New Indent Width... -- Open dialog to change indent width
	Format Paragraph    -- Reformat the current blank-line-separated
                               paragraph

Run Menu (only in Edit window):

	Python Shell -- Open or wake up the Python shell window
	---
	Check Module -- Run a syntax check on the module
	Run Module   -- Execute the current file in the __main__ namespace

Shell Menu (only in Shell window):

	View Last Restart -- Scroll the shell window to the last restart
	Restart Shell     -- Restart the interpreter with a fresh environment

Debug Menu (only in Shell window):

	Go to File/Line   -- look around the insert point for a filename
		             and line number, open the file, and show the line
	Debugger (toggle) -- Run commands in the shell under the debugger
	Stack Viewer      -- Show the stack traceback of the last exception
	Auto-open Stack Viewer (toggle) -- Open stack viewer on traceback

Options Menu:

	Configure IDLE -- Open a configuration dialog.  Fonts, indentation,
                          keybindings, and color themes may be altered.
                          Startup Preferences may be set, and Additional Help
                          Sources can be specified.
			  
			  On OS X this menu is not present, use
			  menu 'IDLE -> Preferences...' instead.
	---
	Code Context --	  Open a pane at the top of the edit window which
			  shows the block context of the section of code
			  which is scrolling off the top or the window.
			  (Not present in Shell window.)

Windows Menu:

	Zoom Height -- toggles the window between configured size
	and maximum height.
	---
	The rest of this menu lists the names of all open windows;
	select one to bring it to the foreground (deiconifying it if
	necessary).

Help Menu:

	About IDLE  -- Version, copyright, license, credits
	IDLE Readme -- Background discussion and change details
	---
	IDLE Help   -- Display this file
	Python Docs -- Access local Python documentation, if
		       installed.  Otherwise, access www.python.org.
	---
	(Additional Help Sources may be added here)

Edit context menu (Right-click / Control-click on OS X in Edit window):

    	Cut              -- Copy a selection into system-wide clipboard,
                            then delete the selection
	Copy             -- Copy selection into system-wide clipboard
	Paste            -- Insert system-wide clipboard into window
	Set Breakpoint   -- Sets a breakpoint (when debugger open)
	Clear Breakpoint -- Clears the breakpoint on that line

Shell context menu (Right-click / Control-click on OS X in Shell window):

	Cut              -- Copy a selection into system-wide clipboard,
                            then delete the selection
	Copy             -- Copy selection into system-wide clipboard
	Paste            -- Insert system-wide clipboard into window
        ---
	Go to file/line  -- Same as in Debug menu


** TIPS **
==========

Additional Help Sources:

	Windows users can Google on zopeshelf.chm to access Zope help files in
	the Windows help format.  The Additional Help Sources feature of the
	configuration GUI supports .chm, along with any other filetypes
	supported by your browser.  Supply a Menu Item title, and enter the
	location in the Help File Path slot of the New Help Source dialog.  Use
	http:// and/or www. to identify external URLs, or download the file and
	browse for its path on your machine using the Browse button.

	All users can access the extensive sources of help, including
	tutorials, available at www.python.org/doc.  Selected URLs can be added
	or removed from the Help menu at any time using Configure IDLE.

Basic editing and navigation:

	Backspace deletes char to the left; DEL deletes char to the right.
	Control-backspace deletes word left, Control-DEL deletes word right.
	Arrow keys and Page Up/Down move around.
	Control-left/right Arrow moves by words in a strange but useful way.
	Home/End go to begin/end of line.
	Control-Home/End go to begin/end of file.
	Some useful Emacs bindings are inherited from Tcl/Tk:
		Control-a     beginning of line
		Control-e     end of line
		Control-k     kill line (but doesn't put it in clipboard)
		Control-l     center window around the insertion point
	Standard Windows bindings may work on that platform.
	Keybindings are selected in the Settings Dialog, look there.

Automatic indentation:

	After a block-opening statement, the next line is indented by 4 spaces
	(in the Python Shell window by one tab).  After certain keywords
	(break, return etc.) the next line is dedented.  In leading
	indentation, Backspace deletes up to 4 spaces if they are there.  Tab
	inserts spaces (in the Python Shell window one tab), number depends on
	Indent Width.  (N.B. Currently tabs are restricted to four spaces due
	to Tcl/Tk issues.)

        See also the indent/dedent region commands in the edit menu.

Completions:

	Completions are supplied for functions, classes, and attributes of
	classes, both built-in and user-defined.  Completions are also provided
	for filenames.

	The AutoCompleteWindow (ACW) will open after a predefined delay
	(default is two seconds) after a '.' or (in a string) an os.sep is
	typed.  If after one of those characters (plus zero or more other
	characters) you type a Tab the ACW will open immediately if a possible
	continuation is found.

	If there is only one possible completion for the characters entered, a
	Tab will supply that completion without opening the ACW.

	'Show Completions' will force open a completions window.  In an empty
	string, this will contain the files in the current directory.  On a
	blank line, it will contain the built-in and user-defined functions and
	classes in the current name spaces, plus any modules imported.  If some
	characters have been entered, the ACW will attempt to be more specific.

	If string of characters is typed, the ACW selection will jump to the
	entry most closely matching those characters. Entering a Tab will cause
	the longest non-ambiguous match to be entered in the Edit window or
	Shell.  Two Tabs in a row will supply the current ACW selection, as
	will Return or a double click.  Cursor keys, Page Up/Down, mouse
	selection, and the scrollwheel all operate on the ACW.

	'Hidden' attributes can be accessed by typing the beginning of hidden
	name after a '.'.  e.g. '_'.  This allows access to modules with
	'__all__' set, or to class-private attributes.

	Completions and the 'Expand Word' facility can save a lot of typing!

	Completions are currently limited to those in the namespaces.  Names in
	an Edit window which are not via __main__ or sys.modules will not be
	found.  Run the module once with your imports to correct this
	situation.  Note that IDLE itself places quite a few modules in
	sys.modules, so much can be found by default, e.g. the re module.

	If you don't like the ACW popping up unbidden, simply make the delay
	longer or disable the extension.  OTOH, you could make the delay zero.

	You could also switch off the CallTips extension.  (We will be adding
	a delay to the call tip window.)

Python Shell window:

	Control-c interrupts executing command.
	Control-d sends end-of-file; closes window if typed at >>> prompt.

    Command history:

	Alt-p retrieves previous command matching what you have typed.
	Alt-n retrieves next.
	      (These are Control-p, Control-n on OS X)
	Return while cursor is on a previous command retrieves that command.
	Expand word is also useful to reduce typing.

    Syntax colors:

	The coloring is applied in a background "thread", so you may
	occasionally see uncolorized text.  To change the color
	scheme, use the Configure IDLE / Highlighting dialog.

    Python default syntax colors:

	Keywords	orange
	Builtins	royal purple
	Strings		green
	Comments	red
	Definitions	blue

    Shell default colors:

	Console output	brown
	stdout		blue
	stderr		red
	stdin		black

Other preferences:

	The font preferences, keybinding, and startup preferences can
	be changed using the Settings dialog.

Command line usage:

	Enter idle -h at the command prompt to get a usage message.

Running without a subprocess:

	If IDLE is started with the -n command line switch it will run in a
	single process and will not create the subprocess which runs the RPC
	Python execution server.  This can be useful if Python cannot create
	the subprocess or the RPC socket interface on your platform.  However,
	in this mode user code is not isolated from IDLE itself.  Also, the
	environment is not restarted when Run/Run Module (F5) is selected.  If
	your code has been modified, you must reload() the affected modules and
	re-import any specific items (e.g. from foo import baz) if the changes
	are to take effect.  For these reasons, it is preferable to run IDLE
	with the default subprocess if at all possible.

Extensions:

	IDLE contains an extension facility.  See the beginning of
	config-extensions.def in the idlelib directory for further information.
	The default extensions are currently:

		FormatParagraph
		AutoExpand
		ZoomHeight
		ScriptBinding
		CallTips
		ParenMatch
		AutoComplete
		CodeContext
