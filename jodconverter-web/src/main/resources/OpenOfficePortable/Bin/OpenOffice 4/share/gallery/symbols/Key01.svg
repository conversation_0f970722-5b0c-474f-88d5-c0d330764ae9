<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 12.0.1, SVG Export Plug-In . SVG Version: 6.00 Build 51448)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" [
	<!ENTITY ns_svg "http://www.w3.org/2000/svg">
	<!ENTITY ns_xlink "http://www.w3.org/1999/xlink">
]>
<svg  version="1.1" xmlns="&ns_svg;" xmlns:xlink="&ns_xlink;" width="100.001" height="100" viewBox="0 0 100.001 100"
	 overflow="visible" enable-background="new 0 0 100.001 100" xml:space="preserve">
<g id="Layer_2">
</g>
<g id="Layer_1">
	<g>
		<g>
			<g>
				
					<linearGradient id="XMLID_10_" gradientUnits="userSpaceOnUse" x1="664.5264" y1="-174.9653" x2="664.5264" y2="-152.2922" gradientTransform="matrix(0.7519 0.6593 -0.6593 0.7519 -533.7977 -243.4064)">
					<stop  offset="0.0048" style="stop-color:#BEBEBE"/>
					<stop  offset="0.4857" style="stop-color:#D6D6D6"/>
					<stop  offset="1" style="stop-color:#BEBEBE"/>
				</linearGradient>
				<path fill="url(#XMLID_10_)" d="M59.074,45.325c-6.295,1.209-11.603,6.508-12.908,12.887c0,0.001-0.319,2.852-0.319,2.852
					l0.299,0.196l38.604,33.886l13.029-2.747L97.44,80.6l-4.665-4.089c0,0-4.263,0.513-5.442,0.654
					c-0.075-1.108-0.315-4.688-0.315-4.688s-1.682-0.247-2.371-0.348c-0.005-0.771-0.021-3.045-0.021-3.045
					s-5.634,0.708-6.929,0.871c-0.007-1.101-0.027-4.361-0.027-4.361s-3.403,0.34-4.541,0.453c0.064-1.417,0.389-8.541,0.389-8.541
					L59.546,45.252L59.074,45.325z"/>
				<path fill="#7F7F7F" d="M59.874,44.543l-0.587,0.017l-0.112,0.003l-0.123,0.003l-0.12,0.022
					c-3.17,0.609-6.243,2.252-8.654,4.627c-2.489,2.452-4.165,5.512-4.848,8.847c-0.161,0.797-0.286,2.462-0.29,2.59l-0.021,0.706
					l0.53,0.466L83.971,95.46l0.565,0.496l0.734-0.155l12.049-2.54l1.226-0.259l-0.036-1.252l-0.31-10.844l-0.019-0.654
					l-0.491-0.431l-4.168-3.654l-0.504-0.44l-0.664,0.08l-4.325,0.52l-0.223-3.296l-0.081-1.208l-1.197-0.175l-1.136-0.167
					l-0.011-1.558l-0.012-1.688l-1.675,0.211l-5.253,0.66l-0.018-2.694l-0.011-1.646l-1.638,0.163l-2.858,0.284l0.333-7.313
					l0.033-0.721l-0.542-0.476L60.316,44.93L59.874,44.543L59.874,44.543z M46.639,60.697c0.002-0.067,0.122-1.648,0.261-2.334
					c1.283-6.272,6.427-11.17,12.315-12.301l0.112-0.003l13.425,11.773l0,0l0,0l-0.412,9.047l1.492-0.148l0,0l0.016-0.002l0,0
					l3.076-0.307l0.019,2.873l0,0v0.011l0,0l0.01,1.499l1.488-0.187l0,0l0.011-0.001l0,0l5.43-0.683l0.021,2.844l2.408,0.353
					l0.227,3.375l0,0v0.002l0,0l0.101,1.496l1.49-0.179l0,0h0.002l0,0l4.403-0.53L96.7,80.95l0.31,10.844l0,0l0,0l-12.049,2.54
					L46.639,60.697L46.639,60.697L46.639,60.697z"/>
			</g>
			<g>
				<path fill="#7F7F7F" d="M37.908,4.167c-0.001,0-0.003,0-0.004,0c-0.418,0-0.817,0.052-1.187,0.156L26.76,6.531l-0.05,0.011
					l-0.049,0.014c-1.33,0.392-3.024,1.477-3.941,2.524L3.733,30.767c-0.913,1.043-1.764,2.864-1.98,4.235l-0.015,0.1l-0.003,0.1
					L1.52,44.574c-0.201,1.61,0.597,3.522,1.888,4.493l0.032,0.022l0.032,0.022l21.926,14.741c0.989,0.723,2.553,1.207,3.914,1.207
					c0.237,0,0.466-0.016,0.683-0.045l13.114-1.003l0.055-0.005L43.219,64c1.435-0.216,3.24-1.204,4.199-2.297l12.401-14.165
					c0.951-1.087,1.707-2.994,1.758-4.438l0.002-0.053l-0.002-0.052l-0.458-13.539c0.033-1.486-0.716-3.366-1.762-4.424
					L41.538,5.597l-0.027-0.029L41.482,5.54C40.618,4.692,39.249,4.167,37.908,4.167L37.908,4.167z M36.666,4.338
					c0.009-0.003,0.018-0.005,0.026-0.008C36.684,4.333,36.675,4.335,36.666,4.338L36.666,4.338z M37.91,5.667
					c0.014,0,0.028,0,0.043,0c0.812,0.01,1.694,0.299,2.291,0.777c0.022,0.018,0.044,0.036,0.066,0.054
					c0.042,0.036,0.083,0.073,0.122,0.112l17.83,19.449c0.76,0.748,1.356,2.207,1.358,3.297c0,0.035-0.001,0.07-0.002,0.104
					l0.46,13.586l0,0l0,0c-0.039,1.1-0.664,2.677-1.388,3.503l-12.4,14.164c-0.725,0.827-2.207,1.64-3.295,1.804l-13.16,1.006
					c-0.164,0.024-0.34,0.037-0.523,0.037c-1.034,0-2.308-0.376-3.056-0.938L4.309,47.867c-0.776-0.583-1.32-1.764-1.32-2.78
					c0-0.136,0.009-0.268,0.029-0.395l0.217-9.457c0.171-1.087,0.903-2.653,1.627-3.48l18.987-21.687
					c0.725-0.828,2.181-1.762,3.236-2.073l10.004-2.218c0.252-0.074,0.53-0.109,0.817-0.109C37.907,5.667,37.909,5.667,37.91,5.667
					L37.91,5.667L37.91,5.667z M25.383,63.841c-0.009-0.007-0.019-0.014-0.028-0.021C25.364,63.828,25.374,63.834,25.383,63.841
					L25.383,63.841z M30.031,65.01c0.01-0.002,0.021-0.003,0.03-0.005C30.051,65.007,30.041,65.008,30.031,65.01L30.031,65.01z"/>
			</g>
			
				<linearGradient id="XMLID_11_" gradientUnits="userSpaceOnUse" x1="570.5088" y1="260.3726" x2="614.0087" y2="298.0392" gradientTransform="matrix(0.9992 -0.0402 0.0402 0.9992 -565.9452 -215.9672)">
				<stop  offset="0" style="stop-color:#E6E6E6"/>
				<stop  offset="1" style="stop-color:#979797"/>
			</linearGradient>
				<linearGradient id="XMLID_12_" gradientUnits="userSpaceOnUse" x1="590.8594" y1="302.8765" x2="590.8594" y2="242.4752" gradientTransform="matrix(0.9992 -0.0402 0.0402 0.9992 -565.9452 -215.9672)">
				<stop  offset="0" style="stop-color:#E6E6E6"/>
				<stop  offset="1" style="stop-color:#979797"/>
			</linearGradient>
				<linearGradient id="XMLID_13_" gradientUnits="userSpaceOnUse" x1="568.1865" y1="277.9907" x2="613.5283" y2="277.9907" gradientTransform="matrix(0.9992 -0.0402 0.0402 0.9992 -565.9452 -215.9672)">
				<stop  offset="0" style="stop-color:#E6E6E6"/>
				<stop  offset="1" style="stop-color:#979797"/>
			</linearGradient>
				<linearGradient id="XMLID_14_" gradientUnits="userSpaceOnUse" x1="582.6748" y1="280.2856" x2="582.6748" y2="258.8641" gradientTransform="matrix(0.9992 -0.0402 0.0402 0.9992 -565.9452 -215.9672)">
				<stop  offset="0" style="stop-color:#E6E6E6"/>
				<stop  offset="1" style="stop-color:#979797"/>
			</linearGradient>
			<g>
					<linearGradient id="XMLID_15_" gradientUnits="userSpaceOnUse" x1="664.6377" y1="-173.1064" x2="664.1661" y2="-154.4929" gradientTransform="matrix(0.7519 0.6593 -0.6593 0.7519 -533.7977 -243.4064)">
					<stop  offset="0.0048" style="stop-color:#BEBEBE"/>
					<stop  offset="0.4857" style="stop-color:#D6D6D6"/>
					<stop  offset="1" style="stop-color:#BEBEBE"/>
				</linearGradient>
				<path fill="url(#XMLID_15_)" d="M96.7,80.949l-4.168-3.653l-5.895,0.709l-0.328-4.873l-2.408-0.354l-0.02-2.844l-6.93,0.871
					l-0.027-4.383l-4.584,0.457l0.412-9.047L59.327,46.058l-0.111,0.003c-5.889,1.131-11.032,6.029-12.315,12.301
					c-0.14,0.687-0.259,2.268-0.261,2.334l38.322,33.638l12.049-2.54L96.7,80.949z"/>
			</g>
			
				<linearGradient id="XMLID_16_" gradientUnits="userSpaceOnUse" x1="629.9961" y1="313.4604" x2="624.5823" y2="318.8748" gradientTransform="matrix(0.9992 -0.0402 0.0402 0.9992 -565.9452 -215.9672)">
				<stop  offset="0.0048" style="stop-color:#BDBDBD"/>
				<stop  offset="0.281" style="stop-color:#D6D6D6"/>
				<stop  offset="1" style="stop-color:#7F7F7F"/>
			</linearGradient>
			<path fill="url(#XMLID_16_)" d="M94.286,92.368L54.224,57.236c0,0-1.301-0.315-2.292,0.815c-0.993,1.131-0.533,2.406-0.533,2.406
				L89.05,93.472L94.286,92.368z"/>
			
				<linearGradient id="XMLID_17_" gradientUnits="userSpaceOnUse" x1="637.3682" y1="305.9966" x2="628.7012" y2="314.6631" gradientTransform="matrix(0.9992 -0.0402 0.0402 0.9992 -565.9452 -215.9672)">
				<stop  offset="0.0048" style="stop-color:#BDBDBD"/>
				<stop  offset="0.281" style="stop-color:#D6D6D6"/>
				<stop  offset="1" style="stop-color:#7F7F7F"/>
			</linearGradient>
			<path fill="url(#XMLID_17_)" d="M96.7,80.949l-4.168-3.653l-5.895,0.709l-0.328-4.873l-2.408-0.354l-0.02-2.844l-6.93,0.871
				l-0.027-4.383l-4.584,0.457l0.412-9.047L61.575,48.03c-0.982,0.245-2.082,0.783-3.063,1.897c-2.508,2.862-2.076,4.8-2.076,4.8
				l40.529,35.539L96.7,80.949z"/>
		</g>
		<g>
			<linearGradient id="XMLID_18_" gradientUnits="userSpaceOnUse" x1="2.9897" y1="34.6118" x2="60.0791" y2="34.6118">
				<stop  offset="0.0048" style="stop-color:#151414"/>
				<stop  offset="0.0237" style="stop-color:#1A1919"/>
				<stop  offset="0.1255" style="stop-color:#323030"/>
				<stop  offset="0.2339" style="stop-color:#434140"/>
				<stop  offset="0.3524" style="stop-color:#4D4B4A"/>
				<stop  offset="0.5" style="stop-color:#504E4D"/>
				<stop  offset="0.649" style="stop-color:#4D4B4A"/>
				<stop  offset="0.7687" style="stop-color:#434140"/>
				<stop  offset="0.8781" style="stop-color:#323030"/>
				<stop  offset="0.9809" style="stop-color:#1A1919"/>
				<stop  offset="1" style="stop-color:#151414"/>
			</linearGradient>
			<path fill="url(#XMLID_18_)" d="M59.618,29.46c0.039-1.099-0.57-2.629-1.355-3.4L40.433,6.61
				c-0.785-0.771-2.289-1.146-3.344-0.834L27.085,7.994c-1.055,0.312-2.511,1.245-3.235,2.073L4.863,31.754
				c-0.725,0.827-1.456,2.393-1.627,3.48l-0.217,9.457c-0.17,1.087,0.411,2.516,1.291,3.175l21.946,14.756
				c0.881,0.66,2.491,1.064,3.579,0.9l13.16-1.006c1.088-0.164,2.57-0.976,3.295-1.803l12.4-14.164
				c0.725-0.827,1.35-2.404,1.389-3.503L59.618,29.46z M32.946,25.277L22.242,37.503c-1.455,1.662-3.982,1.83-5.645,0.375
				l-0.376-0.329c-1.662-1.455-1.83-3.982-0.375-5.644L26.55,19.679c1.455-1.662,3.982-1.83,5.645-0.375l0.377,0.33
				C34.234,21.089,34.401,23.616,32.946,25.277z"/>
		</g>
		<path opacity="0.05" fill="#FFFFFF" d="M37.25,11.403c0.005-0.001,0.014-0.002,0.014-0.002L37.25,11.403z M37.212,11.413
			c-0.045,0.012-0.131,0.034-0.131,0.034s-8.513,1.887-8.798,1.95c-0.459,0.148-1.296,0.686-1.602,1.033L9.705,33.823
			c-0.294,0.336-0.691,1.179-0.798,1.666c-0.011,0.49-0.189,8.287-0.189,8.287s-0.015,0.13-0.022,0.203
			c0.005,0.095,0.078,0.273,0.143,0.356c0.292,0.196,19.523,13.128,19.523,13.128s0.059,0.043,0.061,0.045
			c0.231,0.166,0.912,0.337,1.195,0.301l0.069-0.01l0.124-0.012c0,0,11.284-0.863,11.604-0.888c0.354-0.067,1.074-0.463,1.297-0.717
			l11.087-12.665c0.236-0.27,0.547-1.053,0.574-1.432c-0.01-0.305-0.41-12.066-0.41-12.066s0.002-0.099,0.002-0.107
			c0-0.258-0.236-0.85-0.414-1.037l-0.037-0.039l-0.057-0.059c0,0-15.587-17.006-15.823-17.262
			C37.533,11.457,37.322,11.403,37.212,11.413z"/>
		<g opacity="0.15">
			<path d="M56.382,24.413c0.785,0.771,1.395,2.301,1.355,3.4l0.461,13.585c-0.039,1.1-0.664,2.676-1.389,3.504l-12.4,14.164
				c-0.725,0.828-2.207,1.639-3.295,1.803l-13.16,1.006c-1.089,0.164-2.699-0.241-3.579-0.9L3.537,46.963
				c0.213,0.359,0.475,0.678,0.773,0.902l21.946,14.756c0.881,0.66,2.491,1.064,3.579,0.9l13.16-1.006
				c1.088-0.164,2.57-0.976,3.295-1.803l12.4-14.164c0.725-0.827,1.35-2.404,1.389-3.503L59.618,29.46
				c0.039-1.099-0.57-2.629-1.355-3.4L40.433,6.61c-0.245-0.24-0.564-0.437-0.913-0.592L56.382,24.413z"/>
		</g>
		<g opacity="0.05">
			<path fill="#FFFFFF" d="M5.277,46.667l0.217-9.457c0.17-1.086,0.902-2.652,1.626-3.48l18.987-21.686
				c0.725-0.828,2.181-1.761,3.236-2.072l10.002-2.218c0.688-0.203,1.562-0.111,2.307,0.187L40.433,6.61
				c-0.785-0.771-2.289-1.146-3.344-0.834L27.085,7.994c-1.055,0.312-2.511,1.245-3.235,2.073L4.863,31.754
				c-0.725,0.827-1.456,2.393-1.627,3.48l-0.217,9.457c-0.17,1.087,0.411,2.516,1.291,3.175l1.406,0.945
				C5.354,48.14,5.171,47.338,5.277,46.667z"/>
		</g>
		<path opacity="0.1" fill="#FFFFFF" d="M34.076,20.95L33.7,20.621c-0.193-0.169-0.4-0.312-0.614-0.437
			c1.161,1.483,1.146,3.625-0.14,5.093L22.242,37.503c-1.285,1.468-3.404,1.766-5.027,0.814c0.152,0.193,0.319,0.38,0.512,0.548
			l0.376,0.33c1.662,1.455,4.189,1.287,5.645-0.375l10.704-12.226C35.906,24.933,35.738,22.405,34.076,20.95z"/>
		<path opacity="0.1" fill="#FFFFFF" d="M34.076,20.95L33.7,20.621c-0.193-0.169-0.4-0.312-0.614-0.437
			c1.161,1.483,1.146,3.625-0.14,5.093L22.242,37.503c-1.285,1.468-3.404,1.766-5.027,0.814c0.152,0.193,0.319,0.38,0.512,0.548
			l0.376,0.33c1.662,1.455,4.189,1.287,5.645-0.375l10.704-12.226C35.906,24.933,35.738,22.405,34.076,20.95z"/>
	</g>
</g>
</svg>
