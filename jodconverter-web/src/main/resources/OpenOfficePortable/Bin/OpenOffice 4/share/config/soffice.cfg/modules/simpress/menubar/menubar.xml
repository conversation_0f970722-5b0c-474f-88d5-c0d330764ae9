<?xml version="1.0" encoding="UTF-8"?>
<!--***********************************************************
 * 
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 * 
 *   http://www.apache.org/licenses/LICENSE-2.0
 * 
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 * 
 ***********************************************************-->
<menu:menubar xmlns:menu="http://openoffice.org/2001/menu" menu:id="menubar">
	<menu:menu menu:id=".uno:PickList">
		<menu:menupopup>
			<menu:menuitem menu:id=".uno:AddDirect"/>
			<menu:menuitem menu:id=".uno:Open"/>
			<menu:menuitem menu:id=".uno:RecentFileList"/>
			<menu:menuseparator/>
			<menu:menuitem menu:id=".uno:AutoPilotMenu"/>
			<menu:menuseparator/>
			<menu:menuitem menu:id=".uno:CloseDoc"/>
			<menu:menuitem menu:id=".uno:Save"/>
			<menu:menuitem menu:id=".uno:SaveAs"/>
			<menu:menuitem menu:id=".uno:SaveAll"/>
			<menu:menuseparator/>
			<menu:menuitem menu:id=".uno:Reload"/>
			<menu:menuitem menu:id=".uno:VersionDialog"/>
			<menu:menuseparator/>
			<menu:menuitem menu:id=".uno:ExportTo"/>
			<menu:menuitem menu:id=".uno:ExportToPDF"/>
			<menu:menu menu:id=".uno:SendMenu">
				<menu:menupopup>
					<menu:menuitem menu:id=".uno:SendMail"/>
					<menu:menuitem menu:id=".uno:SendMailDocAsOOo"/>
					<menu:menuitem menu:id=".uno:SendMailDocAsMS"/>
					<menu:menuitem menu:id=".uno:SendMailDocAsPDF"/>
				</menu:menupopup>
			</menu:menu>
			<menu:menuseparator/>
			<menu:menuitem menu:id=".uno:SetDocumentProperties"/>
			<menu:menuitem menu:id=".uno:Signature"/>
			<menu:menu menu:id=".uno:TemplateMenu">
				<menu:menupopup>
					<menu:menuitem menu:id=".uno:Organizer"/>
					<menu:menuitem menu:id=".uno:AddressBookSource"/>
					<menu:menuitem menu:id=".uno:SaveAsTemplate"/>
					<menu:menuitem menu:id=".uno:OpenTemplate"/>
				</menu:menupopup>
			</menu:menu>
			<menu:menuseparator/>
			<menu:menuitem menu:id=".uno:WebHtml"/>
			<menu:menuseparator/>
			<menu:menuitem menu:id=".uno:Print"/>
			<menu:menuitem menu:id=".uno:PrinterSetup"/>
			<menu:menuseparator/>
			<menu:menuitem menu:id=".uno:Quit"/>
		</menu:menupopup>
	</menu:menu>
	<menu:menu menu:id=".uno:EditMenu">
		<menu:menupopup>
			<menu:menuitem menu:id=".uno:Undo"/>
			<menu:menuitem menu:id=".uno:Redo"/>
			<menu:menuseparator/>
			<menu:menuitem menu:id=".uno:Cut"/>
			<menu:menuitem menu:id=".uno:Copy"/>
			<menu:menuitem menu:id=".uno:Paste"/>
                        <menu:menuitem menu:id=".uno:PasteSpecial"/>
			<menu:menuitem menu:id=".uno:SelectAll"/>
			<menu:menuseparator/>
			<menu:menuitem menu:id=".uno:SearchDialog"/>
			<menu:menuseparator/>
			<menu:menuitem menu:id=".uno:CopyObjects"/>
			<menu:menuseparator/>
			<menu:menuitem menu:id=".uno:ToggleObjectBezierMode"/>
			<menu:menuitem menu:id=".uno:GlueEditMode"/>
			<menu:menuseparator/>
			<menu:menuitem menu:id=".uno:ModifyField"/>
			<menu:menuitem menu:id=".uno:DeletePage"/>
			<menu:menuseparator/>
			<menu:menuitem menu:id=".uno:ManageLinks"/>
			<menu:menuitem menu:id=".uno:PlugInsActive"/>
			<menu:menuitem menu:id=".uno:ImageMapDialog"/>
			<menu:menuitem menu:id=".uno:ObjectMenue"/>
			<menu:menuitem menu:id=".uno:EditHyperlink"/>
		</menu:menupopup>
	</menu:menu>
	<menu:menu menu:id=".uno:ViewMenu">
		<menu:menupopup>
			<menu:menuitem menu:id=".uno:NormalMultiPaneGUI" menu:style="radio"/>
            <menu:menuitem menu:id=".uno:OutlineMode" menu:style="radio"/>
			<menu:menuitem menu:id=".uno:DiaMode" menu:style="radio"/>
			<menu:menuitem menu:id=".uno:Presentation"/>
			<menu:menuitem menu:id=".uno:NotesMode" menu:style="radio"/>
			<menu:menuitem menu:id=".uno:HandoutMode" menu:style="radio"/>
   			<menu:menuseparator/>
			<menu:menu menu:id=".uno:MasterPageMenu">
				<menu:menupopup>
					<menu:menuitem menu:id=".uno:SlideMasterPage"/>
					<menu:menuitem menu:id=".uno:NotesMasterPage"/>
        			<menu:menuseparator/>
               		<menu:menuitem menu:id=".uno:MasterLayouts"/>
				</menu:menupopup>
			</menu:menu>
			<menu:menu menu:id=".uno:DisplayQualityMenu">
				<menu:menupopup>
					<menu:menuitem menu:id=".uno:OutputQualityColor" menu:style="radio"/>
					<menu:menuitem menu:id=".uno:OutputQualityGrayscale" menu:style="radio"/>
					<menu:menuitem menu:id=".uno:OutputQualityBlackWhite" menu:style="radio"/>
				</menu:menupopup>
			</menu:menu>
			<menu:menuseparator/>
      <menu:menuitem menu:id=".uno:Sidebar"/>
      <menu:menuitem menu:id=".uno:LeftPaneImpress"/>
			<menu:menuitem menu:id=".uno:AvailableToolbars"/>
			<menu:menuitem menu:id=".uno:StatusBarVisible"/>
			<menu:menuitem menu:id=".uno:ShowImeStatusWindow"/>
			<menu:menuitem menu:id=".uno:ShowRuler"/>
			<menu:menu menu:id=".uno:GridMenu">
				<menu:menupopup>
					<menu:menuitem menu:id=".uno:GridVisible"/>
					<menu:menuitem menu:id=".uno:GridUse"/>
					<menu:menuitem menu:id=".uno:GridFront"/>
				</menu:menupopup>
			</menu:menu>
			<menu:menu menu:id=".uno:SnapLinesMenu">
				<menu:menupopup>
					<menu:menuitem menu:id=".uno:HelplinesVisible"/>
					<menu:menuitem menu:id=".uno:HelplinesUse"/>
					<menu:menuitem menu:id=".uno:HelplinesFront"/>
				</menu:menupopup>
			</menu:menu>
      <menu:menuitem menu:id=".uno:ShowAnnotations"/>
      <menu:menuseparator/>
			<menu:menuitem menu:id=".uno:Navigator"/>
			<menu:menuseparator/>
			<menu:menuitem menu:id=".uno:HeaderAndFooter"/>
			<menu:menuitem menu:id=".uno:Zoom"/>
		</menu:menupopup>
	</menu:menu>
	<menu:menu menu:id=".uno:InsertMenu">
		<menu:menupopup>
			<menu:menuitem menu:id=".uno:InsertPage"/>
			<menu:menuitem menu:id=".uno:DuplicatePage"/>
			<menu:menuitem menu:id=".uno:ExpandPage"/>
			<menu:menuitem menu:id=".uno:SummaryPage"/>
			<menu:menuitem menu:id=".uno:InsertPageNumber"/>
			<menu:menuitem menu:id=".uno:InsertDateAndTime"/>
			<menu:menu menu:id=".uno:FieldMenu">
				<menu:menupopup>
					<menu:menuitem menu:id=".uno:InsertDateFieldFix"/>
					<menu:menuitem menu:id=".uno:InsertDateFieldVar"/>
					<menu:menuitem menu:id=".uno:InsertTimeFieldFix"/>
					<menu:menuitem menu:id=".uno:InsertTimeFieldVar"/>
					<menu:menuseparator/>
					<menu:menuitem menu:id=".uno:InsertAuthorField"/>
					<menu:menuitem menu:id=".uno:InsertPageField"/>
          <menu:menuitem menu:id=".uno:InsertPagesField"/>
          <menu:menuitem menu:id=".uno:InsertFileField"/>
				</menu:menupopup>
			</menu:menu>
      <menu:menuitem menu:id=".uno:InsertAnnotation"/>
      <menu:menuitem menu:id=".uno:InsertSymbol"/>
            <menu:menu menu:id=".uno:FormattingMarkMenu">
                <menu:menupopup>
                    <menu:menuitem menu:id=".uno:InsertNonBreakingSpace"/>
                    <menu:menuitem menu:id=".uno:InsertHardHyphen"/>
                    <menu:menuitem menu:id=".uno:InsertSoftHyphen"/>
                    <menu:menuitem menu:id=".uno:InsertZWSP"/>
                    <menu:menuitem menu:id=".uno:InsertZWNBSP"/>
                    <menu:menuitem menu:id=".uno:InsertLRM"/>
                    <menu:menuitem menu:id=".uno:InsertRLM"/>
                </menu:menupopup>
            </menu:menu>
			<menu:menuitem menu:id=".uno:HyperlinkDialog"/>
			<menu:menuitem menu:id=".uno:AnimationObjects"/>
			<menu:menuseparator/>
            <menu:menu menu:id=".uno:GraphicMenu">
                <menu:menupopup>
                    <menu:menuitem menu:id=".uno:InsertGraphic"/>
                    <menu:menu menu:id=".uno:Scan">
                        <menu:menupopup>
                            <menu:menuitem menu:id=".uno:TwainSelect"/>
                            <menu:menuitem menu:id=".uno:TwainTransfer"/>
                        </menu:menupopup>
                    </menu:menu>
                </menu:menupopup>
            </menu:menu>
			<menu:menuitem menu:id=".uno:InsertTable"/>
			<menu:menuitem menu:id=".uno:InsertAVMedia"/>
            <menu:menu menu:id=".uno:ObjectMenu">
				<menu:menupopup>
					<menu:menuitem menu:id=".uno:InsertObject"/>
					<menu:menuitem menu:id=".uno:InsertPlugin"/>
					<menu:menuitem menu:id=".uno:InsertMath"/>
				</menu:menupopup>
			</menu:menu>
            <menu:menuitem menu:id=".uno:InsertObjectChart"/>
			<menu:menuitem menu:id=".uno:InsertObjectFloatingFrame"/>
			<menu:menuseparator/>
			<menu:menuitem menu:id=".uno:ImportFromFile"/>
		</menu:menupopup>
	</menu:menu>
	<menu:menu menu:id=".uno:FormatMenu">
		<menu:menupopup>
			<menu:menuitem menu:id=".uno:SetDefault"/>
			<menu:menuseparator/>
            <menu:menuitem menu:id=".uno:FontDialog"/>
            <menu:menuitem menu:id=".uno:ParagraphDialog"/>
            <menu:menuitem menu:id=".uno:OutlineBullet"/>
            <menu:menuitem menu:id=".uno:PageSetup"/>
            <menu:menuseparator/>
            <menu:menu menu:id=".uno:TransliterateMenu">
                <menu:menupopup>
                    <menu:menuitem menu:id=".uno:ChangeCaseToSentenceCase"/>
                    <menu:menuitem menu:id=".uno:ChangeCaseToLower"/>
                    <menu:menuitem menu:id=".uno:ChangeCaseToUpper"/>
                    <menu:menuitem menu:id=".uno:ChangeCaseToTitleCase"/>
                    <menu:menuitem menu:id=".uno:ChangeCaseToToggleCase"/>
                    <menu:menuitem menu:id=".uno:ChangeCaseToHalfWidth"/>
                    <menu:menuitem menu:id=".uno:ChangeCaseToFullWidth"/>
                    <menu:menuitem menu:id=".uno:ChangeCaseToHiragana"/>
                    <menu:menuitem menu:id=".uno:ChangeCaseToKatakana"/>
                </menu:menupopup>
            </menu:menu>
            <menu:menuseparator/>
            <menu:menuitem menu:id=".uno:TransformDialog"/>
            <menu:menuitem menu:id=".uno:FormatLine"/>
            <menu:menuitem menu:id=".uno:FormatArea"/>
			<menu:menuitem menu:id=".uno:TextAttributes"/>
			<menu:menuitem menu:id=".uno:GrafAttrCrop"/>
			<menu:menuseparator/>
            <menu:menuitem menu:id=".uno:PresentationLayout"/>
            <menu:menuitem menu:id=".uno:ModifyPage"/>
            <menu:menuitem menu:id=".uno:DesignerDialog"/>
            <menu:menuseparator/>
            <menu:menu menu:id=".uno:GroupMenu">
                <menu:menupopup>
                    <menu:menuitem menu:id=".uno:FormatGroup"/>
                    <menu:menuitem menu:id=".uno:FormatUngroup"/>
                    <menu:menuitem menu:id=".uno:EnterGroup"/>
                    <menu:menuitem menu:id=".uno:LeaveGroup"/>
                </menu:menupopup>
            </menu:menu>
        </menu:menupopup>
	</menu:menu>
	<menu:menu menu:id=".uno:ToolsMenu">
		<menu:menupopup>
            <menu:menuitem menu:id=".uno:SpellDialog"/>
            <menu:menu menu:id=".uno:LanguageMenu">
                <menu:menupopup>
                    <menu:menuitem menu:id=".uno:HangulHanjaConversion"/>
                    <menu:menuitem menu:id=".uno:ChineseConversion"/>
                    <menu:menuitem menu:id=".uno:ThesaurusDialog"/>
                    <menu:menuitem menu:id=".uno:Hyphenation"/>
                    <menu:menuseparator/>
                    <menu:menuitem menu:id=".uno:MoreDictionaries"/>
                </menu:menupopup>
            </menu:menu>
      <menu:menuseparator/>
			<menu:menuitem menu:id=".uno:Gallery"/>
			<menu:menuitem menu:id=".uno:BmpMask"/>
			<menu:menuitem menu:id=".uno:AVMediaPlayer"/>
            <menu:menuitem menu:id=".uno:PresentationMinimizer"/>
			<menu:menuseparator/>
            <menu:menu menu:id=".uno:MacrosMenu">
                <menu:menupopup>
                    <menu:menuitem menu:id=".uno:MacroRecorder"/>
                    <menu:menuitem menu:id=".uno:RunMacro"/>
                    <menu:menu menu:id=".uno:ScriptOrganizer"/>
                    <menu:menuitem menu:id=".uno:MacroSignature"/>
                    <menu:menuseparator/>
                    <menu:menuitem menu:id=".uno:MacroOrganizer?TabId:short=1"/>
                </menu:menupopup>
			</menu:menu>

			<menu:menuitem menu:id="service:com.sun.star.deployment.ui.PackageManagerDialog"/>
			<menu:menuitem menu:id=".uno:OpenXMLFilterSettings"/>
			<menu:menuitem menu:id=".uno:AutoCorrectDlg"/>
			<menu:menuitem menu:id=".uno:ConfigureDialog"/>
			<menu:menuitem menu:id=".uno:OptionsTreeDialog"/>
		</menu:menupopup>
	</menu:menu>
	<menu:menu menu:id=".uno:SlideShowMenu">
		<menu:menupopup>
			<menu:menuitem menu:id=".uno:Presentation"/>
			<menu:menuitem menu:id=".uno:PresentationDialog"/>
			<menu:menuitem menu:id=".uno:RehearseTimings"/>
			<menu:menuseparator/>
			<menu:menuitem menu:id=".uno:AnimationEffects"/>
			<!--menu:menuitem menu:id=".uno:CustomAnimationSchemes" / -->
			<menu:menuitem menu:id=".uno:CustomAnimation"/>
			<menu:menuitem menu:id=".uno:SlideChangeWindow"/>
			<menu:menuseparator/>
			<menu:menuitem menu:id=".uno:ShowSlide"/>
			<menu:menuitem menu:id=".uno:HideSlide"/>
			<menu:menuitem menu:id=".uno:CustomShowDialog"/>
		</menu:menupopup>
	</menu:menu>
  <menu:menu menu:id=".uno:WindowList">
		<menu:menupopup>
			<menu:menuitem menu:id=".uno:NewWindow"/>
			<menu:menuitem menu:id=".uno:CloseWin"/>
			<menu:menuseparator/>
		</menu:menupopup>
	</menu:menu>
	<menu:menu menu:id=".uno:HelpMenu">
		<menu:menupopup>
			<menu:menuitem menu:id=".uno:HelpIndex"/>
			<menu:menuitem menu:id=".uno:ExtendedHelp"/>
			<menu:menuseparator/>
			<menu:menuitem menu:id=".uno:HelpSupport"/>
			<menu:menuseparator/>
			<menu:menuitem menu:id=".uno:About"/>
		</menu:menupopup>
	</menu:menu>
</menu:menubar>
