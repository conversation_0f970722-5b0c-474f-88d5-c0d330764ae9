<?xml version="1.0" encoding="UTF-8"?>
<!--***********************************************************
 * 
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 * 
 *   http://www.apache.org/licenses/LICENSE-2.0
 * 
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 * 
 ***********************************************************-->

<parcel language="JavaScript" xmlns:parcel="scripting.dtd">
  <script language="JavaScript">
    <locale lang="en">
      <displayname value="ShowDialog" />
      <description>
        Example of how to show a dialog from JavaScript
      </description>
    </locale>
    <functionname value="ShowDialog.js" />
    <logicalname value="ShowDialog.JavaScript" />
  </script>
  <script language="JavaScript">
    <locale lang="en">
      <displayname value="ButtonPressHandler" />
      <description>
        Example of handle button press events for the Dialog
      </description>
    </locale>
    <functionname value="ButtonPressHandler.js" />
    <logicalname value="ButtonPressHandler.JavaScript" />
  </script>
</parcel>

