<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 12.0.1, SVG Export Plug-In . SVG Version: 6.00 Build 51448)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" [
	<!ENTITY ns_svg "http://www.w3.org/2000/svg">
	<!ENTITY ns_xlink "http://www.w3.org/1999/xlink">
]>
<svg  version="1.1" id="Layer_1" xmlns="&ns_svg;" xmlns:xlink="&ns_xlink;" width="202" height="196" viewBox="0 0 202 196"
	 overflow="visible" enable-background="new 0 0 202 196" xml:space="preserve">
<g>
	<g opacity="0.25">
		
			<image width="243" height="147" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAPQAAACUCAYAAACk2wSkAAAACXBIWXMAAAsSAAALEgHS3X78AAAA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" transform="matrix(1 0 0 1 -17.8945 69.2046)">
		</image>
	</g>
	<g>
		<g>
			<linearGradient id="XMLID_25_" gradientUnits="userSpaceOnUse" x1="97.5313" y1="146.8633" x2="61.1813" y2="83.6835">
				<stop  offset="0" style="stop-color:#4591D6"/>
				<stop  offset="1" style="stop-color:#5AC0FF"/>
			</linearGradient>
			<polygon fill="url(#XMLID_25_)" points="61.929,84.956 61.929,137.516 99.592,150.381 99.592,97.912 			"/>
			<linearGradient id="XMLID_26_" gradientUnits="userSpaceOnUse" x1="98.3379" y1="94.7231" x2="141.4583" y2="141.5445">
				<stop  offset="0" style="stop-color:#4591D6"/>
				<stop  offset="1" style="stop-color:#5AC0FF"/>
			</linearGradient>
			<polygon fill="url(#XMLID_26_)" points="99.592,97.912 140.03,85.698 140.26,138.303 99.592,150.381 			"/>
			<linearGradient id="XMLID_27_" gradientUnits="userSpaceOnUse" x1="126.6934" y1="78.3491" x2="63.5147" y2="95.6584">
				<stop  offset="0" style="stop-color:#4591D6"/>
				<stop  offset="1" style="stop-color:#5AC0FF"/>
			</linearGradient>
			<polygon fill="url(#XMLID_27_)" points="61.929,84.956 102.089,72.927 140.03,85.698 99.685,97.912 			"/>
			<polygon opacity="0.3" fill="#FFFFFF" points="100.82,150.016 100.776,99.097 140.037,87.313 140.03,85.698 138.18,85.072 
				99.755,96.603 63.74,84.413 61.929,84.956 61.929,86.628 98.096,98.873 98.142,149.887 99.592,150.381 			"/>
		</g>
		<g>
			<linearGradient id="XMLID_28_" gradientUnits="userSpaceOnUse" x1="97.1821" y1="91.1182" x2="60.8322" y2="27.9384">
				<stop  offset="0" style="stop-color:#4591D6"/>
				<stop  offset="1" style="stop-color:#5AC0FF"/>
			</linearGradient>
			<polygon fill="url(#XMLID_28_)" points="61.582,29.211 61.582,81.775 99.244,94.636 99.244,42.169 			"/>
			<linearGradient id="XMLID_29_" gradientUnits="userSpaceOnUse" x1="97.9912" y1="38.9785" x2="141.113" y2="85.8014">
				<stop  offset="0" style="stop-color:#4591D6"/>
				<stop  offset="1" style="stop-color:#5AC0FF"/>
			</linearGradient>
			<polygon fill="url(#XMLID_29_)" points="99.244,42.169 139.685,29.954 139.914,82.561 99.244,94.636 			"/>
			<linearGradient id="XMLID_30_" gradientUnits="userSpaceOnUse" x1="126.3477" y1="22.605" x2="63.1682" y2="39.9145">
				<stop  offset="0" style="stop-color:#4591D6"/>
				<stop  offset="1" style="stop-color:#5AC0FF"/>
			</linearGradient>
			<polygon fill="url(#XMLID_30_)" points="61.582,29.211 101.743,17.183 139.685,29.954 99.338,42.169 			"/>
			<polygon opacity="0.3" fill="#FFFFFF" points="100.474,94.272 100.43,43.353 139.69,31.57 139.685,29.954 137.834,29.329 
				99.408,40.859 63.395,28.669 61.582,29.211 61.582,30.885 97.749,43.13 97.796,94.143 99.244,94.636 			"/>
		</g>
		<g>
			<linearGradient id="XMLID_31_" gradientUnits="userSpaceOnUse" x1="137.9824" y1="161.0859" x2="101.6325" y2="97.9062">
				<stop  offset="0" style="stop-color:#4591D6"/>
				<stop  offset="1" style="stop-color:#5AC0FF"/>
			</linearGradient>
			<polygon fill="url(#XMLID_31_)" points="102.381,99.179 102.381,151.74 140.044,164.604 140.044,112.135 			"/>
			<linearGradient id="XMLID_32_" gradientUnits="userSpaceOnUse" x1="138.79" y1="108.9453" x2="181.9126" y2="155.769">
				<stop  offset="0" style="stop-color:#4591D6"/>
				<stop  offset="1" style="stop-color:#5AC0FF"/>
			</linearGradient>
			<polygon fill="url(#XMLID_32_)" points="140.044,112.135 180.483,99.921 180.713,152.529 140.044,164.604 			"/>
			<linearGradient id="XMLID_33_" gradientUnits="userSpaceOnUse" x1="167.1455" y1="92.5718" x2="103.9656" y2="109.8814">
				<stop  offset="0" style="stop-color:#4591D6"/>
				<stop  offset="1" style="stop-color:#5AC0FF"/>
			</linearGradient>
			<polygon fill="url(#XMLID_33_)" points="102.381,99.179 142.543,87.15 180.483,99.921 140.136,112.135 			"/>
			<polygon opacity="0.3" fill="#FFFFFF" points="141.272,164.24 141.229,113.319 180.489,101.536 180.483,99.921 178.631,99.296 
				140.206,110.825 104.192,98.637 102.381,99.179 102.381,100.85 138.547,113.095 138.594,164.109 140.044,164.604 			"/>
		</g>
		<g>
			<linearGradient id="XMLID_34_" gradientUnits="userSpaceOnUse" x1="137.54" y1="104.959" x2="101.1892" y2="41.7776">
				<stop  offset="0" style="stop-color:#4591D6"/>
				<stop  offset="1" style="stop-color:#5AC0FF"/>
			</linearGradient>
			<polygon fill="url(#XMLID_34_)" points="101.938,43.05 101.938,95.614 139.602,108.477 139.602,56.007 			"/>
			<linearGradient id="XMLID_35_" gradientUnits="userSpaceOnUse" x1="138.3467" y1="52.8169" x2="181.4692" y2="99.6406">
				<stop  offset="0" style="stop-color:#4591D6"/>
				<stop  offset="1" style="stop-color:#5AC0FF"/>
			</linearGradient>
			<polygon fill="url(#XMLID_35_)" points="139.602,56.007 180.041,43.793 180.271,96.4 139.602,108.477 			"/>
			<linearGradient id="XMLID_36_" gradientUnits="userSpaceOnUse" x1="166.7031" y1="36.4438" x2="103.5237" y2="53.7534">
				<stop  offset="0" style="stop-color:#4591D6"/>
				<stop  offset="1" style="stop-color:#5AC0FF"/>
			</linearGradient>
			<polygon fill="url(#XMLID_36_)" points="101.938,43.05 142.101,31.021 180.041,43.793 139.694,56.007 			"/>
			<polygon opacity="0.3" fill="#FFFFFF" points="140.831,108.112 140.788,57.191 180.046,45.409 180.041,43.793 178.189,43.167 
				139.764,54.697 103.75,42.508 101.938,43.05 101.938,44.722 138.106,56.968 138.151,107.982 139.602,108.477 			"/>
		</g>
		<g>
			<linearGradient id="XMLID_37_" gradientUnits="userSpaceOnUse" x1="53.3354" y1="160.1201" x2="16.9865" y2="96.942">
				<stop  offset="0" style="stop-color:#4591D6"/>
				<stop  offset="1" style="stop-color:#5AC0FF"/>
			</linearGradient>
			<polygon fill="url(#XMLID_37_)" points="17.734,98.215 17.734,150.775 55.396,163.639 55.396,111.17 			"/>
			<linearGradient id="XMLID_38_" gradientUnits="userSpaceOnUse" x1="54.1416" y1="107.98" x2="97.2642" y2="154.8037">
				<stop  offset="0" style="stop-color:#4591D6"/>
				<stop  offset="1" style="stop-color:#5AC0FF"/>
			</linearGradient>
			<polygon fill="url(#XMLID_38_)" points="55.396,111.17 95.835,98.956 96.065,151.564 55.396,163.639 			"/>
			<linearGradient id="XMLID_39_" gradientUnits="userSpaceOnUse" x1="82.498" y1="91.6069" x2="19.3194" y2="108.9163">
				<stop  offset="0" style="stop-color:#4591D6"/>
				<stop  offset="1" style="stop-color:#5AC0FF"/>
			</linearGradient>
			<polygon fill="url(#XMLID_39_)" points="17.734,98.215 57.895,86.185 95.835,98.956 55.489,111.17 			"/>
			<polygon opacity="0.3" fill="#FFFFFF" points="56.624,163.273 56.581,112.355 95.842,100.572 95.835,98.956 93.985,98.332 
				55.559,109.862 19.546,97.672 17.734,98.215 17.734,99.887 53.899,112.131 53.947,163.145 55.396,163.639 			"/>
		</g>
		<g>
			<linearGradient id="XMLID_40_" gradientUnits="userSpaceOnUse" x1="52.9878" y1="104.3774" x2="16.6383" y2="41.1985">
				<stop  offset="0" style="stop-color:#4591D6"/>
				<stop  offset="1" style="stop-color:#5AC0FF"/>
			</linearGradient>
			<polygon fill="url(#XMLID_40_)" points="17.387,42.471 17.387,95.032 55.05,107.895 55.05,55.427 			"/>
			<linearGradient id="XMLID_41_" gradientUnits="userSpaceOnUse" x1="53.7964" y1="52.2373" x2="96.9182" y2="99.0602">
				<stop  offset="0" style="stop-color:#4591D6"/>
				<stop  offset="1" style="stop-color:#5AC0FF"/>
			</linearGradient>
			<polygon fill="url(#XMLID_41_)" points="55.05,55.427 95.488,43.211 95.72,95.819 55.05,107.895 			"/>
			<linearGradient id="XMLID_42_" gradientUnits="userSpaceOnUse" x1="82.1514" y1="35.8638" x2="18.9723" y2="53.1732">
				<stop  offset="0" style="stop-color:#4591D6"/>
				<stop  offset="1" style="stop-color:#5AC0FF"/>
			</linearGradient>
			<polygon fill="url(#XMLID_42_)" points="17.387,42.471 57.549,30.441 95.488,43.211 55.142,55.427 			"/>
			<polygon opacity="0.3" fill="#FFFFFF" points="56.278,107.53 56.235,56.613 95.496,44.828 95.488,43.211 93.638,42.588 
				55.212,54.116 19.2,41.928 17.387,42.471 17.387,44.143 53.554,56.388 53.601,107.401 55.05,107.895 			"/>
		</g>
		<g>
			<linearGradient id="XMLID_43_" gradientUnits="userSpaceOnUse" x1="93.7871" y1="174.3457" x2="57.4367" y2="111.1651">
				<stop  offset="0" style="stop-color:#4591D6"/>
				<stop  offset="1" style="stop-color:#5AC0FF"/>
			</linearGradient>
			<polygon fill="url(#XMLID_43_)" points="58.186,112.438 58.186,165 95.849,177.863 95.849,125.395 			"/>
			<linearGradient id="XMLID_44_" gradientUnits="userSpaceOnUse" x1="94.5942" y1="122.2041" x2="137.7168" y2="169.0278">
				<stop  offset="0" style="stop-color:#4591D6"/>
				<stop  offset="1" style="stop-color:#5AC0FF"/>
			</linearGradient>
			<polygon fill="url(#XMLID_44_)" points="95.849,125.395 136.287,113.179 136.517,165.789 95.849,177.863 			"/>
			<linearGradient id="XMLID_45_" gradientUnits="userSpaceOnUse" x1="122.9502" y1="105.8315" x2="59.7716" y2="123.1409">
				<stop  offset="0" style="stop-color:#4591D6"/>
				<stop  offset="1" style="stop-color:#5AC0FF"/>
			</linearGradient>
			<polygon fill="url(#XMLID_45_)" points="58.186,112.438 98.348,100.409 136.287,113.179 95.94,125.395 			"/>
			<polygon opacity="0.3" fill="#FFFFFF" points="97.078,177.498 97.034,126.578 136.293,114.794 136.287,113.179 134.436,112.553 
				96.011,124.084 59.997,111.895 58.186,112.438 58.186,114.109 94.352,126.354 94.398,177.369 95.849,177.863 			"/>
		</g>
		<g>
			<linearGradient id="XMLID_46_" gradientUnits="userSpaceOnUse" x1="93.3452" y1="118.2163" x2="56.9958" y2="55.0374">
				<stop  offset="0" style="stop-color:#143777"/>
				<stop  offset="0.8539" style="stop-color:#1D68AA"/>
			</linearGradient>
			<polygon fill="url(#XMLID_46_)" points="57.744,56.31 57.744,108.871 95.406,121.734 95.406,69.265 			"/>
			<linearGradient id="XMLID_47_" gradientUnits="userSpaceOnUse" x1="94.1509" y1="66.0752" x2="137.2734" y2="112.8989">
				<stop  offset="0" style="stop-color:#143777"/>
				<stop  offset="0.8539" style="stop-color:#1D68AA"/>
			</linearGradient>
			<polygon fill="url(#XMLID_47_)" points="95.406,69.265 135.846,57.052 136.075,109.659 95.406,121.734 			"/>
			<linearGradient id="XMLID_48_" gradientUnits="userSpaceOnUse" x1="122.5088" y1="49.7021" x2="59.3301" y2="67.0115">
				<stop  offset="0" style="stop-color:#143777"/>
				<stop  offset="0.8539" style="stop-color:#1D68AA"/>
			</linearGradient>
			<polygon fill="url(#XMLID_48_)" points="57.744,56.31 97.904,44.281 135.846,57.052 95.499,69.265 			"/>
			<polygon opacity="0.3" fill="#FFFFFF" points="96.636,121.371 96.592,70.45 135.852,58.667 135.846,57.052 133.994,56.427 
				95.569,67.957 59.556,55.768 57.744,56.31 57.744,57.98 93.91,70.226 93.957,121.24 95.406,121.734 			"/>
		</g>
	</g>
</g>
</svg>
