<?xml version="1.0" encoding="UTF-8"?>
<!--***********************************************************
 * 
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 * 
 *   http://www.apache.org/licenses/LICENSE-2.0
 * 
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 * 
 ***********************************************************-->
<menu:menubar xmlns:menu="http://openoffice.org/2001/menu" menu:id="menubar">
	<menu:menu menu:id=".uno:PickList">
		<menu:menupopup>
			<menu:menuitem menu:id=".uno:AddDirect"/>
			<menu:menuitem menu:id=".uno:Open"/>
			<menu:menuitem menu:id=".uno:AutoPilotMenu"/>
			<menu:menuseparator/>
			<menu:menuitem menu:id=".uno:CloseDoc"/>
			<menu:menuitem menu:id=".uno:Save"/>
			<menu:menuitem menu:id=".uno:SaveAs"/>
			<menu:menuitem menu:id=".uno:SaveAll"/>
			<menu:menuitem menu:id=".uno:Reload"/>
			<menu:menuitem menu:id=".uno:VersionDialog"/>
			<menu:menuitem menu:id=".uno:SendMail"/>
			<menu:menuseparator/>
			<menu:menuitem menu:id=".uno:SetDocumentProperties"/>
			<menu:menuseparator/>
			<menu:menuitem menu:id=".uno:Print"/>
			<menu:menuitem menu:id=".uno:PrinterSetup"/>
			<menu:menuseparator/>
			<menu:menuitem menu:id=".uno:Quit"/>
		</menu:menupopup>
	</menu:menu>
	<menu:menu menu:id=".uno:EditMenu">
		<menu:menupopup>
			<menu:menuitem menu:id=".uno:Undo"/>
			<menu:menuitem menu:id=".uno:Redo"/>
			<menu:menuseparator/>
			<menu:menuitem menu:id=".uno:Cut"/>
			<menu:menuitem menu:id=".uno:Copy"/>
			<menu:menuitem menu:id=".uno:Paste"/>
		</menu:menupopup>
	</menu:menu>
	<menu:menu menu:id=".uno:ViewMenu">
		<menu:menupopup>
			<menu:menuitem menu:id=".uno:AvailableToolbars"/>
			<menu:menuitem menu:id=".uno:StatusBarVisible"/>
			<menu:menuitem menu:id=".uno:ShowImeStatusWindow"/>
			<menu:menuseparator/>
			<menu:menuitem menu:id=".uno:DiagramData"/>
		</menu:menupopup>
	</menu:menu>
	<menu:menu menu:id=".uno:InsertMenu">
		<menu:menupopup>
                <menu:menuitem menu:id=".uno:InsertMenuTitles"/>
                <menu:menuitem menu:id=".uno:InsertMenuLegend"/>
                <menu:menuitem menu:id=".uno:InsertMenuAxes"/>
                <menu:menuitem menu:id=".uno:InsertMenuGrids"/>
			<menu:menuseparator/>
			    <menu:menuitem menu:id=".uno:InsertMenuDataLabels"/>
			    <menu:menuitem menu:id=".uno:InsertMenuTrendlines"/>
			    <menu:menuitem menu:id=".uno:InsertMenuMeanValues"/>
			    <menu:menuitem menu:id=".uno:InsertMenuYErrorBars"/>
			<menu:menuseparator/>
			    <menu:menuitem menu:id=".uno:InsertSymbol"/>
		</menu:menupopup>
	</menu:menu>
	<menu:menu menu:id=".uno:FormatMenu">
		<menu:menupopup>
		    <menu:menuitem menu:id=".uno:FormatSelection"/>
			<menu:menuitem menu:id=".uno:TransformDialog"/>
			<menu:menu menu:id=".uno:ArrangeRow">
				<menu:menupopup>
					<menu:menuitem menu:id=".uno:Forward"/>
					<menu:menuitem menu:id=".uno:Backward"/>
				</menu:menupopup>
			</menu:menu>
			<menu:menuseparator/>
			<menu:menu menu:id=".uno:ChartTitleMenu">
				<menu:menupopup>
					<menu:menuitem menu:id=".uno:MainTitle"/>
					<menu:menuitem menu:id=".uno:SubTitle"/>
					<menu:menuseparator/>
					<menu:menuitem menu:id=".uno:XTitle"/>
					<menu:menuitem menu:id=".uno:YTitle"/>
					<menu:menuitem menu:id=".uno:ZTitle"/>
					<menu:menuseparator/>
                    <menu:menuitem menu:id=".uno:SecondaryXTitle"/>
                    <menu:menuitem menu:id=".uno:SecondaryYTitle"/>
                    <menu:menuseparator/>
					<menu:menuitem menu:id=".uno:AllTitles"/>
				</menu:menupopup>
			</menu:menu>
            <menu:menuitem menu:id=".uno:Legend"/>
			<menu:menu menu:id=".uno:DiagramAxisMenu">
				<menu:menupopup>
					<menu:menuitem menu:id=".uno:DiagramAxisX"/>
					<menu:menuitem menu:id=".uno:DiagramAxisY"/>
					<menu:menuitem menu:id=".uno:DiagramAxisZ"/>
					<menu:menuseparator/>
					<menu:menuitem menu:id=".uno:DiagramAxisA"/>
					<menu:menuitem menu:id=".uno:DiagramAxisB"/>
					<menu:menuseparator/>
					<menu:menuitem menu:id=".uno:DiagramAxisAll"/>
				</menu:menupopup>
			</menu:menu>
            <menu:menu menu:id=".uno:DiagramGridMenu">
				<menu:menupopup>
					<menu:menuitem menu:id=".uno:DiagramGridYMain"/>
					<menu:menuitem menu:id=".uno:DiagramGridXMain"/>
					<menu:menuitem menu:id=".uno:DiagramGridZMain"/>
					<menu:menuseparator/>
					<menu:menuitem menu:id=".uno:DiagramGridYHelp"/>
					<menu:menuitem menu:id=".uno:DiagramGridXHelp"/>
					<menu:menuitem menu:id=".uno:DiagramGridZHelp"/>
					<menu:menuseparator/>
					<menu:menuitem menu:id=".uno:DiagramGridAll"/>
				</menu:menupopup>
			</menu:menu>
			<menu:menuseparator/>
			    <menu:menuitem menu:id=".uno:DiagramWall"/>
			    <menu:menuitem menu:id=".uno:DiagramFloor"/>
			    <menu:menuitem menu:id=".uno:DiagramArea"/>
            <menu:menuseparator/>
                <menu:menuitem menu:id=".uno:DiagramType"/>
                <menu:menuitem menu:id=".uno:DataRanges"/>
			    <menu:menuitem menu:id=".uno:View3D"/>
            
		</menu:menupopup>
	</menu:menu>
	<menu:menu menu:id=".uno:ToolsMenu">
		<menu:menupopup>
		    <menu:menu menu:id=".uno:MacrosMenu">
                <menu:menupopup>
                    <menu:menuitem menu:id=".uno:MacroRecorder"/>
                    <menu:menuitem menu:id=".uno:RunMacro"/>
                    <menu:menu menu:id=".uno:ScriptOrganizer"/>
                    <menu:menuseparator/>
                    <menu:menuitem menu:id=".uno:MacroOrganizer?TabId:short=1"/>
                </menu:menupopup>
			</menu:menu>
		    <menu:menuitem menu:id="service:com.sun.star.deployment.ui.PackageManagerDialog"/>
			<menu:menuitem menu:id=".uno:ConfigureDialog"/>
			<menu:menuitem menu:id=".uno:OptionsTreeDialog"/>
		</menu:menupopup>
	</menu:menu>
	<menu:menu menu:id=".uno:WindowList">
		<menu:menupopup>
			<menu:menuitem menu:id=".uno:NewWindow"/>
			<menu:menuitem menu:id=".uno:CloseWin"/>
			<menu:menuseparator/>
		</menu:menupopup>
	</menu:menu>
	<menu:menu menu:id=".uno:HelpMenu">
		<menu:menupopup>
			<menu:menuitem menu:id=".uno:HelpIndex"/>
			<menu:menuitem menu:id=".uno:ExtendedHelp"/>
			<menu:menuseparator/>
			<menu:menuitem menu:id=".uno:HelpSupport"/>
			<menu:menuseparator/>
			<menu:menuitem menu:id=".uno:About"/>
		</menu:menupopup>
	</menu:menu>
</menu:menubar>
