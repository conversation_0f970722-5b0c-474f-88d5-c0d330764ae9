<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 12.0.1, SVG Export Plug-In . SVG Version: 6.00 Build 51448)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" [
	<!ENTITY ns_svg "http://www.w3.org/2000/svg">
	<!ENTITY ns_xlink "http://www.w3.org/1999/xlink">
]>
<svg  version="1.1" id="Layer_1" xmlns="&ns_svg;" xmlns:xlink="&ns_xlink;" width="107" height="100" viewBox="0 0 107 100"
	 overflow="visible" enable-background="new 0 0 107 100" xml:space="preserve">
<g>
	<g>
		<defs>
			<circle id="XMLID_1_" cx="54.278" cy="50.093" r="44.363"/>
		</defs>
		<radialGradient id="XMLID_11_" cx="32.1064" cy="33.7573" r="68.5313" gradientUnits="userSpaceOnUse">
			<stop  offset="0" style="stop-color:#00C109"/>
			<stop  offset="1" style="stop-color:#0D6001"/>
		</radialGradient>
		<use xlink:href="#XMLID_1_"  fill="url(#XMLID_11_)"/>
		<clipPath id="XMLID_12_">
			<use xlink:href="#XMLID_1_" />
		</clipPath>
		<defs>
			<filter id="Adobe_OpacityMaskFilter" filterUnits="userSpaceOnUse" x="3.752" y="-21.954" width="100.297" height="85.939">
				<feColorMatrix  type="matrix" values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"/>
			</filter>
		</defs>
		<mask maskUnits="userSpaceOnUse" x="3.752" y="-21.954" width="100.297" height="85.939" id="XMLID_13_">
			<g clip-path="url(#XMLID_12_)" filter="url(#Adobe_OpacityMaskFilter)">
				
					<linearGradient id="XMLID_14_" gradientUnits="userSpaceOnUse" x1="184.8086" y1="-381.5186" x2="238.9721" y2="-337.9234" gradientTransform="matrix(0.8962 0.4437 -0.4437 0.8962 -290.3355 261.1143)">
					<stop  offset="0" style="stop-color:#FFFFFF"/>
					<stop  offset="1" style="stop-color:#000000"/>
				</linearGradient>
				<path fill="url(#XMLID_14_)" d="M102.208,50.808C89.097,77.291,58.325,88.783,33.479,76.48C8.632,64.18-0.88,32.74,12.231,6.257
					c13.111-26.48,43.883-37.975,68.729-25.672S115.319,24.328,102.208,50.808z"/>
			</g>
		</mask>
		
			<ellipse transform="matrix(0.3991 0.9169 -0.9169 0.3991 51.6616 -36.793)" opacity="0.45" clip-path="url(#XMLID_12_)" mask="url(#XMLID_13_)" fill="#FFFFFF" cx="53.9" cy="21.016" rx="41.105" ry="51.668"/>
		<g clip-path="url(#XMLID_12_)">
			<radialGradient id="XMLID_15_" cx="31.1035" cy="27.1206" r="53.0104" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#87609A"/>
				<stop  offset="0.3253" style="stop-color:#704E84"/>
				<stop  offset="0.739" style="stop-color:#593C6D"/>
				<stop  offset="1" style="stop-color:#513565"/>
			</radialGradient>
			<circle fill="url(#XMLID_15_)" cx="54.278" cy="50.093" r="44.363"/>
		</g>
	</g>
	<g>
		<defs>
			<circle id="XMLID_7_" cx="54.278" cy="50.093" r="44.363"/>
		</defs>
		<clipPath id="XMLID_16_">
			<use xlink:href="#XMLID_7_" />
		</clipPath>
		<defs>
			<filter id="Adobe_OpacityMaskFilter_1_" filterUnits="userSpaceOnUse" x="-11.567" y="-15.775" width="87.347" height="99.065">
				<feColorMatrix  type="matrix" values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"/>
			</filter>
		</defs>
		<mask maskUnits="userSpaceOnUse" x="-11.567" y="-15.775" width="87.347" height="99.065" id="XMLID_17_">
			<g clip-path="url(#XMLID_16_)" filter="url(#Adobe_OpacityMaskFilter_1_)">
				
					<linearGradient id="XMLID_2_" gradientUnits="userSpaceOnUse" x1="-92.9458" y1="135.3916" x2="-38.7828" y2="178.9863" gradientTransform="matrix(0.978 -0.2087 0.2087 0.978 75.1313 -127.7948)">
					<stop  offset="0" style="stop-color:#FFFFFF"/>
					<stop  offset="1" style="stop-color:#000000"/>
				</linearGradient>
				<path fill="url(#XMLID_2_)" d="M88.474,27.115c6.164,28.899-10.816,57.016-37.934,62.801
					C23.427,95.701-3.554,76.963-9.718,48.064C-15.884,19.166,1.099-8.952,28.214-14.737C55.327-20.522,82.308-1.784,88.474,27.115z
					"/>
			</g>
		</mask>
		
			<ellipse transform="matrix(0.8822 0.4709 -0.4709 0.8822 19.6802 -11.1422)" opacity="0.3" clip-path="url(#XMLID_16_)" mask="url(#XMLID_17_)" fill="#FFFFFF" cx="32.106" cy="33.757" rx="41.105" ry="51.669"/>
	</g>
	
		<ellipse transform="matrix(0.9343 -0.3565 0.3565 0.9343 -3.431 14.3979)" opacity="0.7" fill="#FFFFFF" cx="37.343" cy="16.506" rx="8.527" ry="4.939"/>
	
		<ellipse transform="matrix(0.6848 -0.7287 0.7287 0.6848 -9.559 26.3756)" opacity="0.93" fill="#FFFFFF" cx="25.713" cy="24.239" rx="3.119" ry="2.389"/>
</g>
</svg>
