<?xml version="1.0" encoding="UTF-8"?>
<!--***********************************************************
 * 
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 * 
 *   http://www.apache.org/licenses/LICENSE-2.0
 * 
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 * 
 ***********************************************************-->
<menu:menubar xmlns:menu="http://openoffice.org/2001/menu" menu:id="menubar">
	<menu:menu menu:id=".uno:PickList">
		<menu:menupopup>
			<menu:menuitem menu:id=".uno:AddDirect"/>
			<menu:menuitem menu:id=".uno:Open"/>
			<menu:menuitem menu:id=".uno:AutoPilotMenu"/>
			<menu:menuseparator/>
			<menu:menuitem menu:id=".uno:CloseDoc"/>
			<menu:menuitem menu:id=".uno:SaveAll"/>
			<menu:menuitem menu:id=".uno:Save"/>
			<menu:menuitem menu:id=".uno:SaveAs"/>
			<menu:menuseparator/>
			<menu:menuitem menu:id=".uno:Quit"/>
		</menu:menupopup>
	</menu:menu>
	<menu:menu menu:id=".uno:EditMenu">
		<menu:menupopup>
			<menu:menuitem menu:id=".uno:Undo"/>
			<menu:menuitem menu:id=".uno:Redo"/>
			<menu:menuseparator/>
			<menu:menuitem menu:id=".uno:Cut"/>
			<menu:menuitem menu:id=".uno:Copy"/>
			<menu:menuitem menu:id=".uno:Paste"/>
		</menu:menupopup>
	</menu:menu>
	<menu:menu menu:id=".uno:ViewMenu">
		<menu:menupopup>
			<menu:menuitem menu:id=".uno:AvailableToolbars"/>
			<menu:menuitem menu:id=".uno:StatusBarVisible"/>
		</menu:menupopup>
	</menu:menu>
	<menu:menu menu:id=".uno:ToolsMenu">
		<menu:menupopup>
			<menu:menuitem menu:id=".uno:DBIndexDesign"/>
			<menu:menuseparator/>
			<menu:menu menu:id=".uno:MacrosMenu">
				<menu:menupopup>
					<menu:menuitem menu:id=".uno:MacroRecorder"/>
					<menu:menuitem menu:id=".uno:RunMacro"/>
					<menu:menu menu:id=".uno:ScriptOrganizer"/>
					<menu:menuseparator/>
					<menu:menuitem menu:id=".uno:MacroOrganizer?TabId:short=1"/>
				</menu:menupopup>
			</menu:menu>
			<menu:menuitem menu:id="service:com.sun.star.deployment.ui.PackageManagerDialog"/>
			<menu:menuitem menu:id=".uno:ConfigureDialog"/>
			<menu:menuitem menu:id=".uno:OptionsTreeDialog"/>
		</menu:menupopup>
	</menu:menu>
	<menu:menu menu:id=".uno:WindowList">
		<menu:menupopup>
			<menu:menuitem menu:id=".uno:CloseWin"/>
		</menu:menupopup>
	</menu:menu>
	<menu:menu menu:id=".uno:HelpMenu">
		<menu:menupopup>
			<menu:menuitem menu:id=".uno:HelpIndex"/>
			<menu:menuitem menu:id=".uno:ExtendedHelp"/>
			<menu:menuseparator/>
			<menu:menuitem menu:id=".uno:HelpSupport"/>
			<menu:menuseparator/>
			<menu:menuitem menu:id=".uno:About"/>
		</menu:menupopup>
	</menu:menu>
</menu:menubar>

