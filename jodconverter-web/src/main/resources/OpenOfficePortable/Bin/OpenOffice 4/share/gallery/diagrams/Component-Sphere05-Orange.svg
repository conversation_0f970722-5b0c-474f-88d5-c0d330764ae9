<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 12.0.1, SVG Export Plug-In . SVG Version: 6.00 Build 51448)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" [
	<!ENTITY ns_svg "http://www.w3.org/2000/svg">
	<!ENTITY ns_xlink "http://www.w3.org/1999/xlink">
]>
<svg  version="1.1" id="Layer_1" xmlns="&ns_svg;" xmlns:xlink="&ns_xlink;" width="107" height="100" viewBox="0 0 107 100"
	 overflow="visible" enable-background="new 0 0 107 100" xml:space="preserve">
<g>
	<g>
		<defs>
			<circle id="XMLID_1_" cx="53.018" cy="50.359" r="44.362"/>
		</defs>
		<linearGradient id="XMLID_10_" gradientUnits="userSpaceOnUse" x1="-10.0698" y1="37.7417" x2="79.9303" y2="55.7417">
			<stop  offset="0" style="stop-color:#FFC10E"/>
			<stop  offset="1" style="stop-color:#F16422"/>
		</linearGradient>
		<use xlink:href="#XMLID_1_"  fill="url(#XMLID_10_)"/>
		<clipPath id="XMLID_11_">
			<use xlink:href="#XMLID_1_" />
		</clipPath>
		<defs>
			<filter id="Adobe_OpacityMaskFilter" filterUnits="userSpaceOnUse" x="2.491" y="-21.688" width="100.296" height="85.939">
				<feColorMatrix  type="matrix" values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"/>
			</filter>
		</defs>
		<mask maskUnits="userSpaceOnUse" x="2.491" y="-21.688" width="100.296" height="85.939" id="XMLID_12_">
			<g clip-path="url(#XMLID_11_)" filter="url(#Adobe_OpacityMaskFilter)">
				
					<linearGradient id="XMLID_13_" gradientUnits="userSpaceOnUse" x1="183.7959" y1="-380.7212" x2="237.9594" y2="-337.1261" gradientTransform="matrix(0.8962 0.4437 -0.4437 0.8962 -290.3355 261.1143)">
					<stop  offset="0" style="stop-color:#FFFFFF"/>
					<stop  offset="1" style="stop-color:#000000"/>
				</linearGradient>
				<path fill="url(#XMLID_13_)" d="M100.946,51.074C87.835,77.557,57.063,89.049,32.218,76.746
					C7.37,64.445-2.142,33.005,10.97,6.523c13.111-26.48,43.883-37.975,68.73-25.672C104.546-6.846,114.058,24.593,100.946,51.074z"
					/>
			</g>
		</mask>
		
			<ellipse transform="matrix(0.3991 0.9169 -0.9169 0.3991 51.147 -35.4765)" opacity="0.45" clip-path="url(#XMLID_11_)" mask="url(#XMLID_12_)" fill="#FFFFFF" cx="52.639" cy="21.282" rx="41.105" ry="51.666"/>
	</g>
	<g>
		<defs>
			<circle id="XMLID_6_" cx="53.018" cy="50.359" r="44.362"/>
		</defs>
		<clipPath id="XMLID_14_">
			<use xlink:href="#XMLID_6_" />
		</clipPath>
		<defs>
			<filter id="Adobe_OpacityMaskFilter_1_" filterUnits="userSpaceOnUse" x="-12.828" y="-15.51" width="87.346" height="99.065">
				<feColorMatrix  type="matrix" values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"/>
			</filter>
		</defs>
		<mask maskUnits="userSpaceOnUse" x="-12.828" y="-15.51" width="87.346" height="99.065" id="XMLID_15_">
			<g clip-path="url(#XMLID_14_)" filter="url(#Adobe_OpacityMaskFilter_1_)">
				
					<linearGradient id="XMLID_2_" gradientUnits="userSpaceOnUse" x1="-94.2344" y1="135.3887" x2="-40.0714" y2="178.9834" gradientTransform="matrix(0.978 -0.2087 0.2087 0.978 75.1313 -127.7948)">
					<stop  offset="0" style="stop-color:#FFFFFF"/>
					<stop  offset="1" style="stop-color:#000000"/>
				</linearGradient>
				<path fill="url(#XMLID_2_)" d="M87.212,27.38c6.166,28.899-10.816,57.016-37.932,62.801S-4.815,77.229-10.979,48.33
					C-17.146,19.431-0.163-8.686,26.952-14.471S81.046-1.518,87.212,27.38z"/>
			</g>
		</mask>
		
			<ellipse transform="matrix(0.8822 0.4709 -0.4709 0.8822 19.657 -10.5168)" opacity="0.3" clip-path="url(#XMLID_14_)" mask="url(#XMLID_15_)" fill="#FFFFFF" cx="30.845" cy="34.023" rx="41.105" ry="51.667"/>
	</g>
	
		<ellipse transform="matrix(0.9343 -0.3565 0.3565 0.9343 -3.6086 13.9655)" opacity="0.7" fill="#FFFFFF" cx="36.081" cy="16.772" rx="8.527" ry="4.939"/>
	
		<ellipse transform="matrix(0.6848 -0.7287 0.7287 0.6848 -10.1502 25.5399)" opacity="0.93" fill="#FFFFFF" cx="24.451" cy="24.504" rx="3.119" ry="2.389"/>
</g>
</svg>
