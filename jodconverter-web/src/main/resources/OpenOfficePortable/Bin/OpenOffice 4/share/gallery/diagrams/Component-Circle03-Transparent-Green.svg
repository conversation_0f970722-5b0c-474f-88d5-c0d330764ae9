<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 12.0.1, SVG Export Plug-In . SVG Version: 6.00 Build 51448)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" [
	<!ENTITY ns_svg "http://www.w3.org/2000/svg">
	<!ENTITY ns_xlink "http://www.w3.org/1999/xlink">
]>
<svg  version="1.1" id="Layer_1" xmlns="&ns_svg;" xmlns:xlink="&ns_xlink;" width="165.329" height="170.281"
	 viewBox="0 0 165.329 170.281" overflow="visible" enable-background="new 0 0 165.329 170.281" xml:space="preserve">
<g>
	<g>
		<radialGradient id="XMLID_11_" cx="81.8413" cy="87.832" r="71.8813" gradientUnits="userSpaceOnUse">
			<stop  offset="0" style="stop-color:#00C109"/>
			<stop  offset="1" style="stop-color:#0D6001"/>
		</radialGradient>
		<path opacity="0.85" fill="url(#XMLID_11_)" d="M81.84,15.95c-39.699,0-71.879,32.185-71.879,71.882
			c0,39.702,32.18,71.883,71.879,71.883c39.701,0,71.881-32.182,71.881-71.883C153.721,48.134,121.542,15.95,81.84,15.95z
			 M81.84,157.109c-38.266,0-69.279-31.014-69.279-69.278c0-38.262,31.014-69.278,69.279-69.278
			c38.262,0,69.277,31.016,69.277,69.278C151.118,126.096,120.102,157.109,81.84,157.109z"/>
		<g>
			<defs>
				<path id="XMLID_2_" d="M153.051,87.832c0,39.333-31.881,71.215-71.211,71.215c-39.336,0-71.219-31.883-71.219-71.215
					c0-39.329,31.883-71.211,71.219-71.211C121.17,16.62,153.051,48.502,153.051,87.832z"/>
			</defs>
			<radialGradient id="XMLID_12_" cx="87.5825" cy="1.1265" r="170.3992" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#00C109"/>
				<stop  offset="1" style="stop-color:#0D6001"/>
			</radialGradient>
			<use xlink:href="#XMLID_2_"  opacity="0.85" fill="url(#XMLID_12_)"/>
			<clipPath id="XMLID_13_">
				<use xlink:href="#XMLID_2_"  opacity="0.85"/>
			</clipPath>
			<defs>
				<filter id="Adobe_OpacityMaskFilter" filterUnits="userSpaceOnUse" x="44.038" y="121.248" width="75.6" height="75.6">
					<feColorMatrix  type="matrix" values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"/>
				</filter>
			</defs>
			<mask maskUnits="userSpaceOnUse" x="44.038" y="121.248" width="75.6" height="75.6" id="XMLID_14_">
				<g clip-path="url(#XMLID_13_)" filter="url(#Adobe_OpacityMaskFilter)">
					<radialGradient id="XMLID_15_" cx="79.3306" cy="159.3594" r="37.8623" gradientUnits="userSpaceOnUse">
						<stop  offset="0" style="stop-color:#A39F9F"/>
						<stop  offset="1" style="stop-color:#000000"/>
					</radialGradient>
					<path fill="url(#XMLID_15_)" d="M123.54,157.951c0,23.117-18.74,41.855-41.859,41.855c-23.115,0-41.855-18.738-41.855-41.855
						c0-23.119,18.74-41.861,41.855-41.861C104.799,116.09,123.54,134.832,123.54,157.951z"/>
				</g>
			</mask>
			<path opacity="0.5" clip-path="url(#XMLID_13_)" mask="url(#XMLID_14_)" fill="#66FF78" d="M119.637,159.047
				c0,20.877-16.924,37.801-37.797,37.801c-20.877,0-37.803-16.924-37.803-37.801c0-20.879,16.926-37.799,37.803-37.799
				C102.713,121.248,119.637,138.168,119.637,159.047z"/>
		</g>
		<g>
			<defs>
				<path id="XMLID_7_" d="M153.051,87.832c0,39.333-31.881,71.215-71.211,71.215c-39.336,0-71.219-31.883-71.219-71.215
					c0-39.329,31.883-71.211,71.219-71.211C121.17,16.62,153.051,48.502,153.051,87.832z"/>
			</defs>
			<clipPath id="XMLID_16_">
				<use xlink:href="#XMLID_7_" />
			</clipPath>
			<defs>
				<filter id="Adobe_OpacityMaskFilter_1_" filterUnits="userSpaceOnUse" x="34.51" y="-4.13" width="94.656" height="94.658">
					<feColorMatrix  type="matrix" values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"/>
				</filter>
			</defs>
			<mask maskUnits="userSpaceOnUse" x="34.51" y="-4.13" width="94.656" height="94.658" id="XMLID_17_">
				<g clip-path="url(#XMLID_16_)" filter="url(#Adobe_OpacityMaskFilter_1_)">
					<radialGradient id="XMLID_1_" cx="78.6997" cy="43.5894" r="47.4074" gradientUnits="userSpaceOnUse">
						<stop  offset="0" style="stop-color:#A39F9F"/>
						<stop  offset="1" style="stop-color:#000000"/>
					</radialGradient>
					<path fill="url(#XMLID_1_)" d="M134.053,41.823c0,28.945-23.465,52.411-52.412,52.411c-28.941,0-52.406-23.466-52.406-52.411
						c0-28.946,23.465-52.412,52.406-52.412C110.588-10.588,134.053,12.877,134.053,41.823z"/>
				</g>
			</mask>
			<circle opacity="0.6" clip-path="url(#XMLID_16_)" mask="url(#XMLID_17_)" fill="#40FFA8" cx="81.838" cy="43.198" r="47.328"/>
		</g>
		<path opacity="0.3" fill="#FFFFFF" d="M31.163,40.615c27.238-28.367,72.318-29.282,100.693-2.037
			c7.469,7.174,13.014,15.593,16.662,24.605c-3.617-9.725-9.432-18.812-17.416-26.481c-28.371-27.245-73.449-26.33-100.695,2.038
			c-20.063,20.897-24.84,50.855-14.631,76.077C6.547,90.003,11.588,60.995,31.163,40.615z"/>
	</g>
	<g>
		
			<image opacity="0.27" width="267" height="267" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAQwAAAEMCAYAAAAxjIiTAAAACXBIWXMAABH+AAAR/gGTj/zDAAAA
BGdBTUEAAK/INwWK6QAAABl0RVh0U29mdHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAADAgSURB
VHja7J1pctxKkKQDQC2kpLaeC8zfPs0cd07Tf/sCbW39RLI2TGuMeC8Y5R4RiVpYS4QZjItEiUUi
P7h7RmaKVFVVVVVVVVVVVVVVfVt19SN4vPo//+t/38z38n//8z/qF1LAqLpxMNzS73UskBQwqm4f
Dt2Vfu/juf6sIFLAqDovILrgc90NA2NMfN1YAClgVJ0PEAgIXfLPzwWQDCDGBmCkoVIAKWAUIHKA
YHBAH3fB13Yn3AtjIyjQ2wxUxgJIAaMgcQyJLCC64H12zQHHHDsSAcJeQt7PgucIIAWPAsYzQIIp
hs75uDef68n7XQCaU+4Bpgo8UBwcgIwJuDCFUvAoYDwcKKJsAQ1sNPh7Aoie/HlGeZxLYXjXwQDj
QN5mwcIg8uX7KnAUMO5VTbCsAQEBwcECAV0d+TgLjDkZRkZRHJz39+bzCCwHR6l4dqZURwHjrtWE
B4gMEKZrIO/bjz3FcWlgIEjYa2/eHgBE9uTfYWpEIngUOAoYtwYKpCZ6Yi08KAwGAoPz/vR1iyQw
zp1hZICxN+/vATj2zt+zANknrUyBo4Bx06DonOxBD2QGhOlagD9bBOAYHKtyrQzDUxZ7Aow/1w58
fmdAsicgOTi5SGRbCh4FjG8FRR8oCT2wFwYG3scD+BjBBgHjXMqiRWlEFsSCYAc+3gUfMxXC8o8R
hKSVcxQwrg6KLCTswF8krsEBB1IZHQEGy1cyLdss7/CmVw+ONfFUxM4AwrsiFXII4CFIdRQ4Chjn
BoUXXtpQcgEUw5+3S/W5JfhcBA1mQ7pAWWSDzAww2L+XsShIaUSw2Jq3DCJagWTgcQSOgkYB49yg
QDMb1mqgwb9U1wK8b8ExODYEBZySAEXLmg+mTKIsxA5Imy2gcBNZEgSKrbo8kOydgDUzy1LgKGCc
HRSDoyaWABL6WjmwQNYD2Y4ooxgDBSCer5dcN2rWsrTkHFZtIFh4V9a6jAm7UuAoYDRnFAgUKJOw
kFgRSKyIuljMCDG7hjAy2z0ZhbuZblJ2j2nVIcS2ZNTGRgFiY97uiArZf77Pso5RfV9lUwoYFBZe
mNk7oFgQFbECb1fmz7OgaLEYUYt2pm+B3StRZ2rrYrjMLEsEjg0BBoLJDigQO8tic46yKQUMqCrQ
9GgECgQJCwgEDASKheRmN1pgwFqyWxqdsmGv186egQpTIKPKHKYg04JDg2Fj3t8AeHw4YSlrUy+b
8qzAIKrCyyiYmkBwQNcShJ0on0ADyA6eqDnKuxg85gCjd+xadLGp396xWEx1bNXbrQMMfb0D5eGB
o2zKMwIjUBXoxkbZhFUNa/OWwWIBrIcHiQwYolbqDDy8HKNrAGtm/csguIEt6k618NDKYw/CUX19
KFB8fF7vRIGgkBT9vJ7WpnRPCAt28zNQLIiSWBtYrBOgQGqCPUVZAIhmEry1GFlYoJmSaJWtB42B
2DnUyKano9nCOg8eB2VV9iAYtcB4U+974NCKI+zheAZodE8IC09VeNbDwuHP9ULUxZKoiT4Bib3w
vgQ7PbgDAEELvcYrAqMTvKIWQcJrfc+GwCOAhwWHhcabUhofAB4TaDL5xhe18ejQ6J4EFMyDs45M
pCZWn4BYq7dWWehAc7rRtTcXk0ccAjCgXgKvrwDd2PsZ2UXLtCrKMgbHkkQt8qjhbSDwHYjyQDmH
DjzfFSzeA3BYxeGFog9vUbongIU3+4ECTW0/XoyasKBAsNA3tf7/mXz21kqwFmgEDJRhZEDhbXsn
ydmSKATNqAzUEYs+ZgvzOqPgbEiqcw0LifcAHBvw82fgeGiL0j0BLLoELOy06NooiRcCjBWZ9RAl
l0cV0CFI2OYi+1TbJpUFWzfRAoosMLLgYOtregIMts7Ga6UfjKLrCTi02vgAIeg7UB5IcVi1sffs
3aNBo3tgULBgE/VSeKB4BVZk+vv2SadBcTCgYCk+ay6ywGCqAoWbc/bGzAIjA41sKMoUB1uYh7pm
F/K1CW4AKs+CY1IMH8CmeODYkhmV/bMEot2DwoIFm8h+aFi8mOvVKIvp76GcogMymDUXoSajrfiL
qNj+D5GnziqKsfF+6WbAI2NVovU4qBEO9bvYZjiUb9ip1/fg2qi/uzUw3z9DINo9KCy6ABZLBxSv
5n1tQbQcttLXggJ1IKKLqYs9gMVcNZGBxFxgnAKPTDftQFRGtlkOgWMkvytkUd5IxsH6N9xA9N6h
0T0BLFhWsQa24xWoCmtBbE6BFIVVEWzOnyXxO0dRsCBTpG0VqjTCIrpnWs5YiTY/Zut1UN5kZ7LW
SXDoKW2tBG2/RgYcntp4KGh0DwKKzCzIdPOsiapgsLCqojODUc/5b8xNZ2GBpuw8+3EQ3m14ThVx
CWC0qI9WcCxJUI0a6lAjnW6e06H0HoBew8K+1V2jundj76iNu841hge2IEhRTHD48Xn9VNcP9fbV
5BYLlVXoG0xP001PpD/Xb3K9mSfVh+BpPLYwam64OSagMJ74gMls9+8tlstsGuw1su0SM0gH8D15
Dxqvtd07Xa5zfqZ//wz/7eVf5d/f/6sUxg3lFSyreAXX9Gc62FyAnAI1A23A4EfKwlMV3tz+QXLn
jc5WEJmnHTlA+hQFEu2PiqZlUSjKpsVRz4xVjoOxKeKoDa0wpgeDtSk223ioXKN7MFgwCzKphVfn
QllFp3KK0fG6rM046hr0ZjwyT+sUGK55QzpQ6ZzPeQdNRzucedkGA8er4DZ+qw50KPphLIqFhm03
9yzK3UKju3NYoAVjS5BVvBo7YpUFeuLYNF1nFBYSzF54S6hZk1W4HT6Dwy3fdAQkXUNwypbGt4ID
zX6hUFuDY2fAgaDB7Ob0oLDZ1OEeodE9ACzQLMiaKIofQFWsTRhm7YenKFgWEe21sBd/B6wUJO59
is45YpLBg/V0DAlwaED8EL4mSD84bLeoVRtvILfSMLHQCBu9bv132j0ALKaBbi2IVRQ/QKC5NjMg
6ObQm64gUGRaiGdtPPuIkDgRHtntExE4dIahMyu0oHBlMqxePUSYRdHB9m+gNqZ7ie21cRfQ6O4M
FvapYhux1iDU/AnUhb0prG9FU2te6zDLKfbOE+UA8omngsQJ8Mhup4iCUbQ2yLb/vwC10QOLspHj
2bE/b/8y6uPd2FRrTY8U5q3+zrs7g4XNLBZyPAvyahTFDwCL6UawMyDWfngdf3bXJt2pyUBxkOQ6
jtqhOoRHZiOkQY47RZcKCC/iLwdYqftFrxeafpe6Q5RNqSNo6KlzGnbf4j3Q3QksrFf1ws0fBBbT
TWCbsA7gl2+DrXcgMe3S520y0KSgKEg03RdZcOjdvKbf/Vr8pQHIsuiHTA+s6zuwJxYcLAy1dvVm
oTHcESxQf4UFhW7E+qU+92pk5qBgoS2InWP/y7z97YAj2mTFBcW9NfB8R/35Gf25/jQ8kQdedNwC
aq/3FvGhTAlBiX1sLRQqt6nu1pq7hjuGhZaUunPzl3zt3rSwYAHWu/GgCBYRKPYFipsFhwjfXHlM
5Ezo3vT2AGGHOmX2HrlZaCxuNFdhsNDAQLBg7d12msw25LwZOflmrmihEdoGr6zHhWv6Of7Pg2YE
A3L6met2frvoLNozFe1opiGkp+IXEh/g1DUojAPLt0ph/KMu7A/apt3Wc7I1IT9MwKnXgRyMBdGq
wiqK30HA6U6PlaK4GcUhgVVhB0B5rfkiuc2DGCzCRrxbVBnDDcFCAmWRWUA2AeOFwGLvwOKvRFZR
oLhPcIyOXRHye/Q2+R2dwNU75S0DMGH30i1AY7hBWOhdp+3hQRYWvxqUxV5wa28ECy/UpCdiFShu
VnGMAThEchsSRSqjJ0ojk7PcrNIYbhAWaBGZhcVPR1msACxsXvFb2Y8WWKRURYHiJsHhnSIfnVUb
7T/igSJzkHbTpszfCY1vBYb6RTIr4sHiF1AWGhYiXzd9tc01HiysBUFt3VRVVN0eOBJqI5qSnaM0
vNkSD1jMRn15TU8FjGTIOTezmGChm2oiWNi8ItqnsVTFY6kNCeAhTqbRAo2MPQoD0e9SGcM3wsIL
Ob2pU6suXh1YbIgN+e1YkGywWarisdRGq+JAg7mTNqXBoOTB41tD0OEGYKFDTtvFaZXFr0TAqWGB
ws3fjqrYCu/WPHoilKp4WIsSZRzMMmR3TWezN83bLF4bGlcHBskt7PqQCRg/HCsyNWZ5syEo4Izy
ip0Em7eWqng4aIhjAbJKw1oTuxCONXOdBIxr5xlXBQbILezKU7v5zU8DC7Q2xM6GoDUh/23syLsT
boY7PZeqeMhcw8s2spZkbLAlGhrRbA2zS1dXGcOVYSHCp0+tFXmVfxaQ6YVkeunxIlAWaE3Imxzv
YZGGRdXT5RpeodCT5XM9AclooMF2YXPt0LWgMXwTLPSMyCB8A5xfwIbo6VMGCzYbYm0Ia8YqWBQ0
WJbBYIFsCdpSUH/OsyXZYy+vqjSuAozG3EIHnf9iQk626pTB4rfkeizCcLPqqaGBwskIFCL+YU1d
AKaDxA1lV88zLg6MmVZksiF6VkQfLDTBAq0N8RaQFSyq5kCjk7aDnrxtBBFAvLwkOrTqy9deWmUM
V4ZF58DixcBCT6N6jVlTr0U0G7IpWFRdEBpdYEmyKkNAnpFZCDdew5pcFBgJKxKtE/klX2dEdMhp
uzhRZtHSvSkFi6pkpuHBosWSoCDU7uGRCUGvZk0uBozAiuhdnKNFZTq30CGnVhZo1Wk2s6gei6oM
NDLgYArDO3S6A9bEZhloW0E95Xq02c6lVMbFgAHUhbcZjgWGbtjSIaeeEdGbrrK1IZmmrC+qonos
qiw0EmpjbABHH4BD//vR/qNXnzW5CDCMumAnlGVOU38huYXeg9NbSBZlFmVBquZalEwQ2iWh0Yk/
c+LB4uBB4+aB0TArwhaW2f04p9zCrhHRsyAs5MwEnAWLqlZooCC0m6kwBuEhKJot0ddegg7Uc6uM
S20CzPwb28jXXpnt9d7FPywG2ZDUBiVVVUHpYLIzecJO4sOV7Bk7EywWyr4v5esGxexCB2axBW63
pTCS6iLaxFf3W0zfH+rkjBqzUgvJSl1UnWhNTrEjA4EJUhlWWWSsydlVRn9hdZE5JsCepr4SvhGO
3Y/TKouCRdXFS903ozOo7fmr0b07HV9xUGMT7ZRvlbg+I3iQ/NaA36swgLpgxxqyaVR79qmeFdEr
UG1uwTbA2Rcsqr5JaWT7MpBFQbMmkcpgMydnVxnDhWDhtX9HQafdDMeeTOaFnJuCRdUN2hNrUbxc
w061RtZkL8mp1nNA45yWhAWdCBprIK3W8vUkKb2/hQ46PTkXNmYVLKquYE/soN7J18O+I2uyJ9Zk
RcbNpOAXgVL5foUxcxo1Oktkyi6sFUH9FmyNSLV7V32X0ugSD1OrNBaBNUEqY5+wJnJOa3IuhXGK
ulgpOvbqxe0VDD6UwngHoPByi4JF1bWVBhrYWmVY1Tw9FG1wfzBjSauMtRyHn9M4ulgAepLCSKiL
aHEZUxcTLHTI+TvILXYk9ClYVF1baaAzSGyOgbqg7YXyDKQwmNLQ/SFnURnnaNxC6gJZEh16ro3/
QupiK1+no9CeFqk1IlVVVy7U0NXJP01dEyQ2aozojaSWQCVMX2MnEDZEbU+Ku5czNnOdO/S0nmwR
wAIda6g7OiMrgmxI5RZVt2JNhKiCnWNN7ENxr/4tdBTHCxhP2pYMN2FJyAIzBAqv7wKdKaKTZLuB
L9vfoqxI1T1akz6wJQtgTSyE9uRyA9C5tuRUS+IlvlZdaAqugLrwuuN0GKTJG+5tUVV1g9Zksgpb
BYZ3Y0uQWuiIylirMbNSY2TxOZ56+SdAFclvOXgehREcdRh1dXpB506+LiybzhPJBJ1lRapuUWWI
+FOtNqOw+8ZYlYG6P7Wy2AGlAXccn6MyTskw2DTqYIKcFVAXOtQR9aJtc8uHURdboy7GsiJVd5Bn
CLhX9+ae35j7/cPJMjo5PvzLKnetTrzT1y4LDKUumMqwi8xWxpqsjMQS+brAbOP80LxDksuKVN2y
NRHh/Rno3tcPTB3wTzaH9TitATRQTwYbzxdRGCy7QGeNrIgn64262CUoi7KLav2uugeVgVrHMypj
I8fNXCJf981gY20pfqt4s8o4dVrVW8aOlIXuuciqC9TJWVak6lGsCVtvwhT2wagMDxp2ehUtarsc
MIx88fa90I0oa+HNKFZdbOR4RsQeD6DVhZQVqbpDayJAZezIOLCzg0hloAhgaVQGVRgttmSOwrDr
+212YQPPJfBTeqqpRV2w7KLURdU9qgwEDWZPULOi7QBdOhEACj+lVW2kgZEIOwcCDJRddIK73iJ1
QVfhVVXdocpAtsRTGTrHs92fVtnbmRK7f+is8LNVYURhZ6Quevk6h2xbZO1V6qKqVMax6rYzJnad
ycXCz1NnSaKwk82MjMKnkywsSl1UPbPK8B6iOgBdmDGoH9xnCz9TwGiwI0sijbR/EuFTqaUuqkpl
cJXhhZ8sy0D9GLNtSYvCYLsGDU5+YbcNm35IGhZbh6KlLqqeWWVskyrDZhkLkmN4G+uk1MYpsyRR
2IlmRkS+zo7sktlF9V1UPbrKsC3jaIcuND5GozIWgjPEKMc4jyUJei8GwVvxLR1gZGGhm1Sq76Lq
0VWG3SWLQWNLoGFtyULwuhLvaMbQlmQVBuu9sDJoQWChZ0fYJiLIitgNfUtdVD2qyhDhq0+RykAb
XrPlGWx6tXm25NRpVZZfoHX8Nr/YkotlF7XArOqRVQbaPHjnqAzUk9Elsgy3H+NSwOiFt4OzzjJJ
SC0PFqUuqp41y9g5D1c7vWp7ohbCg89maPQz8wt7jAAimu3sZHbEKovwMKKqqgdWGXbnbzRrwoAh
8nV9CYMGUxnh2pKMwrAEQupi4YQrHfBnlpoMFkfHvZW6qHpglSHi75mxc+z7CFzAIqEwejDeL2ZJ
0HZiSwIMBAv7PmvSKmVR9SxKA9mSvRknkS3pg3GJdhM/a+jpbcdnVcYAJI+nLqzCQFak7EjVs9iS
FpWxk3i2BO0Pig5JOi3DIO3gzI4shHeTCckvrA1BsCg7UvXMtoS1jSOVjmZL+mCM0vCT5RiRwogO
kEVnKAzGG3l7GNomLXd2pKrqCW1JFhZe+OmpDGRJuktkGAMhmPVHIl/7L+wLZ2GnlB2pKlviWhNk
5bUt0SpjCCyJRLDIAiOrMFB2IYSSyJJoQh7KjlSVLfkyFjyVgRSGzTIWzjhN5xh9Q36RCT0RMJik
Yj0XNTtSVUojn2Ogh651AwsyTt2VqyjH6JPKQsQPPT114fkwdBZk2ZGqsiXH48dCYw9svp0ssAtE
kcJg6qI7JcNAsOiT3wCaUrUvFimLagWvKlty/NA9SD4DZI4A5RgpW9ICDAsO9J9H+UWkMMqGVFV9
VRpsxsSqCy/HiPLG9E7i2WlVSyumMpi6OJAXeHDURcGjqmDxdRyxPGMvfGq1C9xA07EDR8AIAs8+
sCY9URjIe4UzI2VHqsqWuAoDjSUEjAGMWWZH3OCzD5SFtSIZP9SBFxu9wGrWqqriamNsAAfqx4jG
LVtL0p2SYXSETEzejI0Ko0BRVeXnGIfG8YRyjF780FOaLEmgNliO0Qf5hX2hmeyi4FFVsPB35IrU
hSTGbNMO4n0DKJCy0B1kzJKwgMaCJOPnqqqeNcdgD+FM8Mm6s71p1dl9GF1gSfogvzg411j5RVVV
U45xcKwJs/dzVEasMD4T0e4EaHiWJBN2VlVVxdDI2H2WY0QzJEdjX8+UZGZJoiXuXpeY9+IKFFVV
bVlGpDTQGJPkuO0yWcacoxKz6iJrSaTAUVV1kj3xFnBmncJZtuiLFAajUQSMUWp2pKqK1mfwidRF
pNyZgrfjt0lZzFEYQsDB/pND4mKAGGuGpKqKKoxDkGFMfycau037eZ6iMCIaMQKWqqiqOo8diTIN
pi66E8b1rAwj849nEl0ms6qqqjAsogdyZlKh2YboWjSAQhrVRTa/kAJFVdVJCqOlr4mNZ5ET9sPI
LETpki/IgqIataqq5qsLpDK8hzP7NzLjuZujMCRBJK/Dk31csKiqOl+GMScftGM3tCWLC7wgCRRG
ZRdVVecFiGdHvAwjDYrIkkhCniBV0ULCyi6qqubZkjkKY2wc37MURjfzRc2RSqU0qqpyiqLFrsiM
B/RJ06pd9h9LQiP6IVTTVlWVHHV7npJltMLiLJakhXznCGSqqqpOsy6enZlV/QW+2YJAVdX1bcro
qPuzVX8hQJSqqKp6wJoDjHMM/IJGVdUdqvm+fu5VVXdZTatM7x0YXf2+q6qew5K00G/2qriqqqpZ
Y00uOd76M32zpR6qqm4HIN7nToJIBhjjjG/2lE06OnC+a1XV01VyF//sGMtCYjwFGHNaSSNoZFRK
VVVV21jJwKIFGuOplmRMvqC5CqPAUVWVsxqXUBgpcdC6vD2zYIztMl7BZ1XVZRRHL/5u4GjMjnNi
h8UMUHiNW1lY1MxJVdV8QJy8mS954Ifg6Bs8DPqHR0cy9YZ8zYemVFVVUTuB1IV31nGXcAuhTcko
DLY7FvqcpzCio9mqqqrmKYw+sCXReE7vftcaemYWk7Ue3JzesbiqqlTGkbpoPWD5pEWh/QmQ8P7T
6PzG6NS0qqqqtgwje9bxSXvVtHZ6ovNG2H+gJdJAXlBfsKiqaoYGUxWDGVvewz86rvQqCqPFkvSO
Haluz6qnLtDliex7iyWxsJi1Z82cDOOUY+UHQ8Fq3qqqmpdfdEBZRDORY+M4TgMjok/mNPaOyCVG
wQJFVRWHRZRZDI6Cbzm61FUaX4CR3KU4C4usJSlgVFXlwBHZ/cFRGHMObv7/Y1/v4p9drerBYi/4
LEf74gZjSQocVVXtsOiJzfesvh2/e8kdkt6UYdiGDnRW6j6glDdLUl2fVVXnA4edJYke+HvBMyb6
65qAwfKMQ0LeCAlmWlRGzZRUPWWZGZJMdjE4dmR0HEF07vFFgLEn/3kXvKiyI1VVsaLwoDE4D2MG
DG/Mnr01fPrH9+RiDVyRyihgVFW1WZEhUBh27LIxq8ER1hEw1EwJu/aKUnsny8jCopRGVdW83MIb
U52TXdi3dLzbc46zCsOzI4hYLPhcfF4ZpVE5RlXlF1xdDGBMocCTjdWWqdU0MNhS2D1QGmh6tSMk
tC+ybElVVZxfWGAsGvKLKEZIbaRzToXBgs8ueJFlS6qq2uyIHkcLyQeeF1cYAtSF/s93nxf6Rqwl
sbZk4UCjqqrqKzQ8O7IQf0rVqoodcQXz1pIEwSei1c7AAwWf9gUuktakcoyqZ88v9AM1M4asK9iB
McqaLmng2aIwsn4om2MglTHI/MNXqqoePb8YgjG0mJFfnEdhOHYEKQ1GrtH8P1mFATcwLZVR9UTq
QoRPo0ZjiNkRPU69hq2L7OnJbAmaXkWh51JdKM+o8LPq2TMLpC4WZPxYlc7yi12gLuYrDCfHYNSy
SgPZkkVw1YK0qqqv4PDUxdKMHQQLb4zuWYaB8otWhSGSDz31HK+VV5aQS8nNmJQtqXpWO8IUhmdH
MuOzeV/POdOqntLYGoWhVUYP6Ghf+EJwAFpqo+oZ7Ugf2BELDU9hbCU3Q3IWYFhw6IYPT/Kgpe7o
RVulUX0ZVc8MDRR0emOG9V9EkcF5dw1P5hhW8iCSifjBJ7Ml1ZNR9Ux2pFVhWEsigfI/Kb9oVRhs
5dt0bck3hmzJ9IJXgcSyRxFUVT2ysrAKY4KFHS8r9T6yIwgWW4mXcpwtw2B5BpI9Fhx6erUDCmNl
wBHZklIZVY+oLhAskKpAY2UaWyy7sA/y5unUUxRGFHoiorE2cQ2MlVRPRtXzqotM7wUbK71RF3sw
FjN25HRgkKMH0PSqBsYGfIMjUBgrcNkwp1RG1TOqC2tHPIVh8wv9AN8kgPH3uPbyizmWhC1zR5ZE
E+5grMngZBkryQWgVVWPpi7YzMjKse4deICjMWjtyEXOVm21JVujMmyWoUk6SarpB7H+vLxp1lIZ
VY+uLthsIoKGDTt3QOnrMXmSHUkDI7AlLMew36idLRmA1LIezdtgp6rqkdXFglj2lYFFZ+wIgsVZ
7MgchcFsCcowNsSaWJWxDHKMyjKqHl1ddEB52wepVeA2u9g5Kj/q8kzXqdOqLbYEhZ8D+aGgeeZS
GVXPoC4GoC7WwIr0gqdS0QP75NmRZmAYWxKpjA24rDXJzpiUyqh6dHWBcgtvPLCZETbutoG6SNmR
uQqD5Rh2psR+03aNSWdoujZXqYyqZ1AXg/AlEyswJlBnJ4oDWIaBzlG9jCUJ1pZkVMZO+ArWZWBL
SmVUPZq6sHt1MlDo7GJB1MWWqIuT1o6cU2F4sNgGLwBBw/6QXkplVD1RdrEw48A+PNG6ER12MivC
tpy4vMIgwGCrV7fBCzgkVIYNe5ZSfRlVj6UuWPs3sudaXei1XHqcfZDsAimM5hpav+Df3/9L/u3l
X/UPwNKSpb7s4BXvHAUdlCL/ZV949+d7+/M9VlXdICxQyLlUYHj9n+vH5/VTvX1VtkTbke0nIN4+
r9+f1/TxuxxPOOi1XU125FSFIWaQZ1SGzjL0XhmdfJ1iXTeojNoDtOqerAg7ckPPhrwoW46yi1GO
m7Q+Eupidtg5W2EYlYF8WbQfITvarQMWx04HeTsdj6Uyqm5cXfQJdfHTvH1VD81pvO4VKLSqQOpi
Q6DRrC7OpTCYldDkezcv5MNIpVGOw89X9QN7/aTti3ydXmJrTSrPqLplK2Lziune1pbk1dzrtqtz
o8bSuxpb78L7L8ZTX9c5+jC8A462Joz5MNLJ7v05/UC1LXlRF5o1Qftm6F9aVdV3wEJIxmd7Lux9
vgYPxinrQ01a+gGMujvZUYjXsyTGlnQSnwc5KAUxSHzi2QgsyCFpS/TGw1L2pOqbYGGtCOrknCCh
g05tRV7k60Kz6UGsVcVvc+kHsj3y429QzLEj8vnNn8OOiJJLloSTylh+vsjV5wtim+V0hr6Ztf0W
HLr9fKzbuOrKhXILts+FVdGRutgCdfFhVMbZ1o6cTWE0qoxOqQl7qGwvuCFLhG/YwzYBgT+QUhlV
36AuWG6xVJB4Ncrip8rs9DRq9zn4NSgmRfGXUhoWGjsBjVpz1cU5MgyUZdjOT9uJ9mHIaDtARakM
lmVoCi+FB6B/w6fyjKpvggVrzloLDvRfBAedOruYxtA7UBdbOeGgoosqDEdl2B+cpW1PVMag/p4I
D1SRwrApcOUZVbeUW6AZQNuk9UOBYyFfp1EnULwBdfF2DXVxToWBBjdbp/9hCGmnWacXidaYICLb
1ll2REEpjaprwsKqixVRzPqeXgteYIaUxds1s4uzKYwGlYHyjN7JMtAxiaNREyNQGKyhq5RG1TVg
oe9v1m/x01xaXazU1x+UukCzIqi3CU2nnkVdnFtheFkGmjvWpNS01B7MtoxrIr/OyDNKaVRdGhZR
bvFC7mO9V2cvX9u/tR1BTVpIXZy8MvViCsOoDAEKA/1AWZ4xOINdK4YDURpj4gdVSqPqWrDQoLD9
Fr/ka1en7bnYKhuCcgvb2cmatc6iLs6uMMA35W3hZ3MMm/puicqwU1LWA7I8owO/5KqqudU52QXK
LVDrt1XHqOeCjRMGiotYkbMrjKmAykAk7uR4KXwPsoxB+GrUUXjLq5djjPrfKpVRdYK66NSD1yoL
G9T/INmFnUYdDSzelLL4SykNO1mgl1l8uf/PeX+fHRgzplktNBAwmDWxnZ0o/BTBZ6qUNak6hxXx
TlpHrd+/BDdo6SnUnbEiFhZvBBYXVRcXAUZCZYghs6cyFhJvycemc9F00lh5RtUFYGF3jVsLXyfy
M8gtdiS30LCwsyLUkpz7nr4IMEgAGlkTpDQYMFgI6tkUBoyCRtWpsEDK4jWAhbYi05qnKeN7B1bk
zVgRvXx9h+75c6uLiwFjhjXJAAOpjM6AwOvDiKaW/oZGgaNqAoV68GUWlEW5hYXFEsCCqQvW0XkV
K3JxYDRaE5Zp2HZxb0s+pCSysDgKsgoaBQt7TwSwsOtDvOasaH9OBgu0+dRVrMhVgEG28mNv2aKd
XvCeGd4xAxkbElmmgkbBIuohQk1ZCBZ6RkTvcaFhgbo5W2ZF5JJW5CrAANYkyjPsxwvBwWjXCI1s
jQWNggV5qGVhYRuz9IwI2xAHLVnX6sJ2dV41t7gqMAJr0gW5xiB+MBqBY47SKGgULE6BhZ0+RetE
ptwCrUL1ZkRYC/jFrchVgRHMmnTkcxoYDBTsfQ8GXqZCbUpB4+ltiLdbljcjYpuzdMipO57/cnKL
dzne1Pfq6uJqwADWpHMGrQ2YUBjaBSrDKo4x8f+GnytoPCUs0GHJ6xNhsTe5xV9AXfyW4xZwesbI
NWBxVWAESsOTgsyqMIiwTCQCQxdYl5p2fUBQkGnTzEHJrw4sXiVu+/ZyC93N+SH4uICLtX/fDDCC
PEOcPEOET712geJg/35kRzpH+RQ0HktVdBKfTGb34mSzIQwWIjzkZNmF3UEL5hbXUhffAoyEymCK
w2v0Yp2jEXyYHUp9X6U2HjavQA1ZKwcWvxwbMsHCKgu0qExv5mu7OQ/flVt8KzCCPMO2fGeURpew
Jt6/4/WGRLlGqY37hEUX5BUTKBZyvKUe2tMiA4ud8F4Lu8fFh+CTy74lt/h2YDQojS7IOKKAdI7y
kMCydMyiFDjuIq/IqgqbV7CFZFlYfJDMIgOL8Ttzi5sAhpNnRE93Tyl0ATwiBSLC+zpSeUdB4+5U
BdtOLzNtGmUWFha216JFWXxbbnEzwEiuavUGqzfYM1OxmWnZLgmxUhv3oyoiC9LSY/HaAIu/AhvC
Qs7xFmDx7cBosCaZhquWpfNMbUSrYtlsC1UbBY5vB0W2tyJq82YWBPVZoPUhXq+FXSNizxQ53AIs
bgIYyRC0mwGRaBk9Wk7fojqi3KPAcVug8CwI29WbhZt6x3q9NmQEAedvideIoCMCbiLkvElgnEFp
jIFdifbdYMojAxEJbEuB47ZAMSj7gVTFqwIDUhb20CELi2jqFPVabMXvtfi2kNPW4gZ///Y0eCr5
ydeg4xJHdZN4a1TQ8XaDemuX2+/kn4NypzS7E3xs49+dpFMfwC08MR4gzIxAjbZLWMrx4cjszBB0
Bo49nUxvgLNxAk67RmQj8c7f4y3dK8Mt3QCNTV0aDt5mv94MiLcJsWdZsrMsUorjJhSFtxbkxagK
dG6I3oNz2inLHpS8CWyI3mIPHUB0uHVY3BwwEtDoErCgRyQ60OiF71zeJy1MNF1b4LgeKHqjCq39
0L0VOqv4kbAgC/V/o3NPoz4Lr+V7vGVYsDDxFuVmR2wDkpSv4CbQc+Vr9XW9fE222UFL9hduw6qN
+lrvyDq79yLdr6PsytF9kLEeyH7YadOlug9ezBUdwWnvl6lnQsPiTfzt9W6+1+IugZGAht7afUXC
qx9GUv4wN8ICPDF0cLUBkLDg2JhrZ54cOt/IgOPp4eFAIsooOpJDsUORNSjs51ZyfN6pziuQssgc
lqzvkcM9weImLUnSnqABlj0BLZpRYSGodxZsF9iU7L4dMAd5FssCLEd2k2ivpwItR/d6K16dcFOr
0A+Jp03Rxr3IhtwFLG5eYThKA900C/VEsNu9W8WBUm89+EejNnT6/WEsiT0TViffW6I2vIOkvRkf
eSTlYZQEsxzZsFrPZllwaHhMa0LW5h7QW/9rVaGnTNF5p29AUegjAT7U/eNtrzfew++0u8Obywu5
FkGuocGhj6rTFmUwTxSbbWzUjWDB8QF8qrUpOwUjloxHRz3eJUACQCDL4amLTvC+FZ7KmOCgrena
2A99Fo62qTvzu39TsLCgQHkFsiF3BYu7AkYDNAbhrb7o1Hd2inYP1IYHjg+iNLTa0CdVtYIj2tAY
7hT2HTchAEMGEF4+0ZGMAqmKlVGaFhYv8nWKVIOC/d5tEP4GgPFOLIj+nd81LO4OGAE07BPHQgM1
5vwwIFmTG0irDf20seBAF4KGvomsRN0nwZHdET3cLb3lZnVgkL2vugY1wTKlhRx3ba4MMPTvfW2g
oWGhf89IVWhYvBkbYmfPPggsUPB9d7C4S2A40GC5xhLcPF4335qoDT0AtUqwimMDgMHAgfKNvZOe
eyHu2AqKmX+3O+Hvzl3rMwjfNm8JQIFgsTaqQ3fy6u8LqQqkLJD9QAcN7Z3f493lUHcJDAANAdIV
Jec2NUetwGwO3k6tHUwQpufkEUA2wKbsHMWRBYc3CzSeCIdT7qNMz0QnuT6KAdhN/TBA4aZVEivz
tdp+eKriHcBCf07/jrcAFnY6Xe4VFncNDCCP2dPJ3mioNThq3LFqA4FjZwDAQLFx1MY2qTgOSXBk
bMt4xvuGKQiR/GLAQfgSdKsqkAXR7+u/o0NRCwrUf5Nt3PsgVtNr2LvbGa67BsaMXGMRZBsvQHnY
WRS9QtHmGwcCjq0DDNQpypq/EDTsW5HcCfannj+bzSOi2Y5o8d/CAGNFlAWCxNJYD5tT6BWmaBaE
NeuhrGL3aHnFwwKjMdcYBM/Re+CwFsX2bXRGcRzMoN8aOCCQoGwDgYPNrLROz14SGNEO730QZiJY
MFWxJJZjQayHEDtpVUUEChZsPkxe8dDAILlGNB2HwKEBwaBhn149CEYPxK7sADS2CYuyC2ZVmAQ+
x0zLKbkE659gYWbGgiwNIJYA5AgUUWD9DvIJtG6IqQqm9h6mvf+hgOHkGgwcVurqtQZrAw4Nk5X4
c/idsQZ7ojp2BBbbpEXZA+WRURuRXZk+zuzanskmLCS8mY9FEhj279j9SjoAcDQlvjFQ0KCY3m4k
XmRIO3YfaS3QwwEjaVGibMOCA7URr4kU7olVsXZlT+CB3rfAiMCBbEprN2kEjKya6BOg8GwIet9a
jsH8/0Kypenn+W5UBWrxfycBdSarGB8NFA8PjIRF6SRevBQ1/7wAL428cw/CyD2BB7o8a6KvnTOj
4oHDUxoZZdEnAs2BXAtiRbxrABmSzZFGMntlu3NRW3/GfuwFbNL7yLB4eGAEFsVTGwtgU9bOtQKh
KErnrUy2WYcd/FZRtKiMfRCKHs4AjD4INTM2ZAAwQG97wauDR6AoLIA3IKuIOnOzWxU8tAV5OmA4
FkWA2kDTejZoQ6Bg0FiBm57lHKNRBfsZF1ufkg1FM8DIbnMYWZEhUB2D4C0TO2A7RqDW7MyU7ot5
E9yRq/MkNjulf1fy6BbkaYHRqDbQDtMLAA7UXYjAsSTgYKrDAwj62JtybYFGKzAiWHhKg+010oMc
qAvUmVVgGwKLDxVybgAotH1hYfLTqYqnBUZDtoF8N2oeQvBAHy9Joj8Q1SGO+hgdEOyDv5Pt12D3
ypz+is4BCPozASBFasIqqy1RFajj9l2Otx9g06SHZ8wqChhtaqNzArtMMxG6lgE4PNntKRAPKmND
8HkOS8J2Huudv981vN59AArWUYs6azMbHHk/p6fcd/VpgRGoDda74e0X6cEDNRzZfgMmze33xbYn
FHJzH5KfOxUY0ec6kCFNr6dzgLgXvwkuasH3GuNYBy3rqRifFRQFDF9tCLnxke/W6f5Scp2JrI15
AezKQAai9/sbkzA5BzAyUBDn+8zkNmjaeUtgwRrhtkaVoPxn9OzHs8OigNFuU7xNgu0S7AUBCLvY
1OJAvD8CSB8MTpH2jk+RXIfnHDiMThaDppNtQxsDg22As/9Wy9YBBYoCxkk2hc2qoL0bFsR+LEmm
wdQGO6qRHaokziAeAxUS3SvdCf+HF9yi3hLWwMZa6NkaHK+F3gVogaKAcapNaVEdCyfzYB8vxW9i
8qYio2McvUGdBYYEisLLUexsjh3ErFFtS95Hn9sTUIyS38WsVEUB4yrg6MBAbuluZG3QETS8wLRL
KIEIFh40PMvDpn33JKNgNgStpckuyGtuWCtQFDAuCQ4RPkMwSG49xUBsCLMl6Jp2F5tjUyQAR+bv
67cHEmBGHaw78tZriz8kIRG2whcoChjfCQ5vQ1t2qtrQAAjva9kpbJcGRqsF2Qc5w078tTLeLmTp
RXYFigLGtcGRtSudE1ZGbdODk2Ggr2HQOPfvHQWbY9KKHByVgFRDtL9ppoO1QFHAuDvV4a3F6ISv
rYjWZkSzJxlrklUYYwMwDsk8w85gZDZAzjah1axHAePuVEcrQLwFXkMAGc+OeOeFRMDIdppmwRGt
eWkFRKmJAsbDqI4IHiJ8rwlvsVdmDYcHjFMtSQQMth9H685gGUiUmihgPKTqyCoQEX+dBlvY5f1b
5/j9j06OoQcxyxhawNB00luBooDxbPAQ8ddmRPtTeGeDnPv3PjrQQNmG93ciOBQkChgFjwaAeEAR
oyykARTdDEBkwCESL4TLfC38vwsSBYwCSAyQLEyy9mPOPZA5dnFsfFuAKGBUXRAg3sddw+/5nMDw
Bn3mdPkCRAGj6oIAmaMcLvE7n3PQ89GfFSAKGFW3BZHvKAiNgkMBo+r2QXL1KjBUVVVVVVVVVVX5
9f8EGAB2+PyultuxpgAAAABJRU5ErkJggg==" transform="matrix(0.6133 -2.910000e-004 2.910000e-004 0.6133 2.2837 7.958)">
		</image>
		<path fill="#FFFFFF" d="M82.336,15.397C42.241,15.415,9.752,47.933,9.772,88.028c0.02,40.093,32.539,72.58,72.633,72.56
			c40.094-0.018,72.58-32.535,72.563-72.631C154.952,47.863,122.432,15.375,82.336,15.397z M82.403,156.684
			c-37.936,0.018-68.707-30.723-68.721-68.661c-0.021-37.937,30.717-68.704,68.656-68.722c37.936-0.02,68.707,30.722,68.723,68.658
			C151.079,125.893,120.338,156.666,82.403,156.684z"/>
	</g>
</g>
</svg>
