<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 12.0.1, SVG Export Plug-In . SVG Version: 6.00 Build 51448)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" [
	<!ENTITY ns_svg "http://www.w3.org/2000/svg">
	<!ENTITY ns_xlink "http://www.w3.org/1999/xlink">
]>
<svg  version="1.1" id="Layer_1" xmlns="&ns_svg;" xmlns:xlink="&ns_xlink;" width="282.667" height="262.667"
	 viewBox="0 0 282.667 262.667" overflow="visible" enable-background="new 0 0 282.667 262.667" xml:space="preserve">
<g>
	<radialGradient id="XMLID_40_" cx="141.1201" cy="151.5498" r="70.1707" gradientUnits="userSpaceOnUse">
		<stop  offset="0" style="stop-color:#FFFFFF"/>
		<stop  offset="1" style="stop-color:#606060"/>
	</radialGradient>
	<path opacity="0.5" fill="url(#XMLID_40_)" d="M141.118,81.379c-38.753,0-70.168,31.417-70.168,70.17
		c0,38.757,31.416,70.172,70.168,70.172c38.755,0,70.172-31.415,70.172-70.172C211.291,112.796,179.874,81.379,141.118,81.379z
		 M141.118,200.611c-27.096,0-49.061-21.965-49.061-49.063c0-27.096,21.965-49.061,49.061-49.061
		c27.098,0,49.064,21.965,49.064,49.061C190.182,178.646,168.216,200.611,141.118,200.611z"/>
	<path opacity="0.5" fill="none" stroke="#8C8C8C" stroke-width="3.689" stroke-dasharray="7.0981 5.6785" d="M208.479,104.865
		c0,52.103-30.062,94.34-67.148,94.34c-37.084,0-67.15-42.237-67.15-94.34c0-52.1,30.066-94.339,67.15-94.339
		C178.417,10.526,208.479,52.766,208.479,104.865z"/>
	<path opacity="0.5" fill="none" stroke="#8C8C8C" stroke-width="3.595" stroke-dasharray="6.9173 5.5338" d="M68.842,119.771
		c45.122-26.051,95.978-22.447,113.588,8.056c17.607,30.502-4.694,76.344-49.815,102.396
		c-45.125,26.051-95.978,22.438-113.586-8.057C1.419,191.666,23.722,145.822,68.842,119.771z"/>
	
		<ellipse transform="matrix(-0.5 0.866 -0.866 -0.5 424.0589 106.3908)" opacity="0.5" fill="none" stroke="#8C8C8C" stroke-width="3.6336" stroke-dasharray="6.9914 5.5931" cx="181.317" cy="175.611" rx="64.624" ry="95.1"/>
	<g>
		<radialGradient id="XMLID_41_" cx="58.5684" cy="199.0264" r="37.7578" gradientUnits="userSpaceOnUse">
			<stop  offset="0" style="stop-color:#0D69C8"/>
			<stop  offset="1" style="stop-color:#1B3962"/>
		</radialGradient>
		<path opacity="0.85" fill="url(#XMLID_41_)" d="M58.568,161.268c-20.854,0-37.757,16.906-37.757,37.759
			s16.903,37.758,37.757,37.758c20.853,0,37.758-16.905,37.758-37.758S79.42,161.268,58.568,161.268z M58.568,235.421
			c-20.101,0-36.392-16.294-36.392-36.395c0-20.098,16.291-36.392,36.392-36.392s36.393,16.294,36.393,36.392
			C94.961,219.127,78.668,235.421,58.568,235.421z"/>
		<g>
			<defs>
				<circle id="XMLID_3_" cx="58.568" cy="199.026" r="37.408"/>
			</defs>
			<linearGradient id="XMLID_42_" gradientUnits="userSpaceOnUse" x1="51.8389" y1="162.8262" x2="66.0829" y2="239.4487">
				<stop  offset="0" style="stop-color:#011F69"/>
				<stop  offset="0.1768" style="stop-color:#011C60"/>
				<stop  offset="0.4652" style="stop-color:#011548"/>
				<stop  offset="0.8271" style="stop-color:#000A20"/>
				<stop  offset="1" style="stop-color:#00040B"/>
			</linearGradient>
			<use xlink:href="#XMLID_3_"  opacity="0.85" fill="url(#XMLID_42_)"/>
			<clipPath id="XMLID_43_">
				<use xlink:href="#XMLID_3_"  opacity="0.85"/>
			</clipPath>
			<defs>
				<filter id="Adobe_OpacityMaskFilter" filterUnits="userSpaceOnUse" x="38.712" y="216.579" width="39.712" height="39.714">
					<feColorMatrix  type="matrix" values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"/>
				</filter>
			</defs>
			<mask maskUnits="userSpaceOnUse" x="38.712" y="216.579" width="39.712" height="39.714" id="XMLID_44_">
				<g clip-path="url(#XMLID_43_)" filter="url(#Adobe_OpacityMaskFilter)">
					<radialGradient id="XMLID_45_" cx="57.2505" cy="236.5996" r="19.8886" gradientUnits="userSpaceOnUse">
						<stop  offset="0" style="stop-color:#A39F9F"/>
						<stop  offset="1" style="stop-color:#000000"/>
					</radialGradient>
					<circle fill="url(#XMLID_45_)" cx="58.486" cy="235.857" r="21.987"/>
				</g>
			</mask>
			<circle opacity="0.6" clip-path="url(#XMLID_43_)" mask="url(#XMLID_44_)" fill="#0683F4" cx="58.568" cy="236.435" r="19.856"/>
		</g>
		<g>
			<defs>
				<circle id="XMLID_8_" cx="58.568" cy="199.026" r="37.408"/>
			</defs>
			<clipPath id="XMLID_46_">
				<use xlink:href="#XMLID_8_" />
			</clipPath>
			<defs>
				<filter id="Adobe_OpacityMaskFilter_1_" filterUnits="userSpaceOnUse" x="33.707" y="150.718" width="49.722" height="49.724">
					<feColorMatrix  type="matrix" values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"/>
				</filter>
			</defs>
			<mask maskUnits="userSpaceOnUse" x="33.707" y="150.718" width="49.722" height="49.724" id="XMLID_47_">
				<g clip-path="url(#XMLID_46_)" filter="url(#Adobe_OpacityMaskFilter_1_)">
					<radialGradient id="XMLID_1_" cx="56.9185" cy="175.7881" r="24.9037" gradientUnits="userSpaceOnUse">
						<stop  offset="0" style="stop-color:#A39F9F"/>
						<stop  offset="1" style="stop-color:#000000"/>
					</radialGradient>
					<circle fill="url(#XMLID_1_)" cx="58.465" cy="174.857" r="27.532"/>
				</g>
			</mask>
			<circle opacity="0.6" clip-path="url(#XMLID_46_)" mask="url(#XMLID_47_)" fill="#0683F4" cx="58.568" cy="175.58" r="24.861"/>
		</g>
		<path opacity="0.3" fill="#FFFFFF" d="M31.949,174.227c14.307-14.905,37.987-15.383,52.891-1.073
			c3.923,3.77,6.837,8.191,8.753,12.926c-1.901-5.109-4.955-9.882-9.148-13.91c-14.902-14.311-38.581-13.831-52.893,1.07
			c-10.542,10.977-13.048,26.714-7.684,39.964C19.018,200.164,21.667,184.93,31.949,174.227z"/>
	</g>
	<g>
		<radialGradient id="XMLID_2_" cx="140.3862" cy="55.8965" r="37.759" gradientUnits="userSpaceOnUse">
			<stop  offset="0" style="stop-color:#0D69C8"/>
			<stop  offset="1" style="stop-color:#1B3962"/>
		</radialGradient>
		<path opacity="0.85" fill="url(#XMLID_2_)" d="M140.387,18.137c-20.855,0-37.76,16.906-37.76,37.759s16.904,37.76,37.76,37.76
			c20.854,0,37.758-16.907,37.758-37.76S161.241,18.137,140.387,18.137z M140.387,92.289c-20.101,0-36.394-16.292-36.394-36.393
			s16.293-36.393,36.394-36.393s36.39,16.292,36.39,36.393S160.488,92.289,140.387,92.289z"/>
		<g>
			<defs>
				<circle id="XMLID_13_" cx="140.387" cy="55.896" r="37.408"/>
			</defs>
			<linearGradient id="XMLID_4_" gradientUnits="userSpaceOnUse" x1="133.6577" y1="19.6963" x2="147.9021" y2="96.3208">
				<stop  offset="0" style="stop-color:#011F69"/>
				<stop  offset="0.1768" style="stop-color:#011C60"/>
				<stop  offset="0.4652" style="stop-color:#011548"/>
				<stop  offset="0.8271" style="stop-color:#000A20"/>
				<stop  offset="1" style="stop-color:#00040B"/>
			</linearGradient>
			<use xlink:href="#XMLID_13_"  opacity="0.85" fill="url(#XMLID_4_)"/>
			<clipPath id="XMLID_5_">
				<use xlink:href="#XMLID_13_"  opacity="0.85"/>
			</clipPath>
			<defs>
				<filter id="Adobe_OpacityMaskFilter_2_" filterUnits="userSpaceOnUse" x="120.531" y="73.449" width="39.713" height="39.71">
					<feColorMatrix  type="matrix" values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"/>
				</filter>
			</defs>
			<mask maskUnits="userSpaceOnUse" x="120.531" y="73.449" width="39.713" height="39.71" id="XMLID_6_">
				<g clip-path="url(#XMLID_5_)" filter="url(#Adobe_OpacityMaskFilter_2_)">
					<radialGradient id="XMLID_7_" cx="139.0693" cy="93.4697" r="19.889" gradientUnits="userSpaceOnUse">
						<stop  offset="0" style="stop-color:#A39F9F"/>
						<stop  offset="1" style="stop-color:#000000"/>
					</radialGradient>
					<circle fill="url(#XMLID_7_)" cx="140.305" cy="92.728" r="21.988"/>
				</g>
			</mask>
			<path opacity="0.6" clip-path="url(#XMLID_5_)" mask="url(#XMLID_6_)" fill="#0683F4" d="M160.244,93.307
				c0,10.966-8.89,19.853-19.856,19.853c-10.968,0-19.856-8.887-19.856-19.853c0-10.969,8.888-19.858,19.856-19.858
				C151.354,73.449,160.244,82.338,160.244,93.307z"/>
		</g>
		<g>
			<defs>
				<circle id="XMLID_18_" cx="140.387" cy="55.896" r="37.408"/>
			</defs>
			<clipPath id="XMLID_9_">
				<use xlink:href="#XMLID_18_" />
			</clipPath>
			<defs>
				<filter id="Adobe_OpacityMaskFilter_3_" filterUnits="userSpaceOnUse" x="115.525" y="7.589" width="49.721" height="49.722">
					<feColorMatrix  type="matrix" values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"/>
				</filter>
			</defs>
			<mask maskUnits="userSpaceOnUse" x="115.525" y="7.589" width="49.721" height="49.722" id="XMLID_10_">
				<g clip-path="url(#XMLID_9_)" filter="url(#Adobe_OpacityMaskFilter_3_)">
					<radialGradient id="XMLID_11_" cx="138.7373" cy="32.6567" r="24.9031" gradientUnits="userSpaceOnUse">
						<stop  offset="0" style="stop-color:#A39F9F"/>
						<stop  offset="1" style="stop-color:#000000"/>
					</radialGradient>
					<path fill="url(#XMLID_11_)" d="M167.815,31.727c0,15.207-12.326,27.533-27.532,27.533c-15.204,0-27.53-12.326-27.53-27.533
						c0-15.202,12.326-27.529,27.53-27.529C155.489,4.198,167.815,16.525,167.815,31.727z"/>
				</g>
			</mask>
			<circle opacity="0.6" clip-path="url(#XMLID_9_)" mask="url(#XMLID_10_)" fill="#0683F4" cx="140.386" cy="32.449" r="24.861"/>
		</g>
		<path opacity="0.3" fill="#FFFFFF" d="M113.764,31.094c14.311-14.902,37.989-15.382,52.894-1.071
			c3.924,3.769,6.839,8.191,8.756,12.925c-1.901-5.109-4.955-9.881-9.148-13.91c-14.902-14.312-38.584-13.832-52.895,1.069
			c-10.542,10.978-13.047,26.716-7.684,39.966C100.837,57.037,103.485,41.799,113.764,31.094z"/>
	</g>
	<g>
		<radialGradient id="XMLID_12_" cx="223.6411" cy="199.8467" r="37.7583" gradientUnits="userSpaceOnUse">
			<stop  offset="0" style="stop-color:#0D69C8"/>
			<stop  offset="1" style="stop-color:#1B3962"/>
		</radialGradient>
		<path opacity="0.85" fill="url(#XMLID_12_)" d="M223.64,162.088c-20.854,0-37.757,16.905-37.757,37.758
			c0,20.854,16.902,37.76,37.757,37.76c20.854,0,37.759-16.906,37.759-37.76C261.399,178.993,244.494,162.088,223.64,162.088z
			 M223.64,236.24c-20.1,0-36.392-16.294-36.392-36.395c0-20.098,16.292-36.392,36.392-36.392c20.099,0,36.394,16.294,36.394,36.392
			C260.034,219.946,243.739,236.24,223.64,236.24z"/>
		<g>
			<defs>
				<circle id="XMLID_23_" cx="223.64" cy="199.846" r="37.407"/>
			</defs>
			<linearGradient id="XMLID_14_" gradientUnits="userSpaceOnUse" x1="222.7056" y1="162.4541" x2="224.5753" y2="237.2432">
				<stop  offset="0" style="stop-color:#011F69"/>
				<stop  offset="0.1768" style="stop-color:#011C60"/>
				<stop  offset="0.4652" style="stop-color:#011548"/>
				<stop  offset="0.8271" style="stop-color:#000A20"/>
				<stop  offset="1" style="stop-color:#00040B"/>
			</linearGradient>
			<use xlink:href="#XMLID_23_"  opacity="0.85" fill="url(#XMLID_14_)"/>
			<clipPath id="XMLID_15_">
				<use xlink:href="#XMLID_23_"  opacity="0.85"/>
			</clipPath>
			<defs>
				<filter id="Adobe_OpacityMaskFilter_4_" filterUnits="userSpaceOnUse" x="203.784" y="217.398" width="39.713" height="39.714">
					<feColorMatrix  type="matrix" values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"/>
				</filter>
			</defs>
			<mask maskUnits="userSpaceOnUse" x="203.784" y="217.398" width="39.713" height="39.714" id="XMLID_16_">
				<g clip-path="url(#XMLID_15_)" filter="url(#Adobe_OpacityMaskFilter_4_)">
					<radialGradient id="XMLID_17_" cx="222.3237" cy="237.4209" r="19.889" gradientUnits="userSpaceOnUse">
						<stop  offset="0" style="stop-color:#A39F9F"/>
						<stop  offset="1" style="stop-color:#000000"/>
					</radialGradient>
					<circle fill="url(#XMLID_17_)" cx="223.559" cy="236.68" r="21.988"/>
				</g>
			</mask>
			<circle opacity="0.6" clip-path="url(#XMLID_15_)" mask="url(#XMLID_16_)" fill="#0683F4" cx="223.64" cy="237.254" r="19.856"/>
		</g>
		<g>
			<defs>
				<circle id="XMLID_28_" cx="223.64" cy="199.846" r="37.407"/>
			</defs>
			<clipPath id="XMLID_19_">
				<use xlink:href="#XMLID_28_" />
			</clipPath>
			<defs>
				<filter id="Adobe_OpacityMaskFilter_5_" filterUnits="userSpaceOnUse" x="198.78" y="151.541" width="49.721" height="49.721">
					<feColorMatrix  type="matrix" values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"/>
				</filter>
			</defs>
			<mask maskUnits="userSpaceOnUse" x="198.78" y="151.541" width="49.721" height="49.721" id="XMLID_20_">
				<g clip-path="url(#XMLID_19_)" filter="url(#Adobe_OpacityMaskFilter_5_)">
					<radialGradient id="XMLID_21_" cx="221.9907" cy="176.6064" r="24.9027" gradientUnits="userSpaceOnUse">
						<stop  offset="0" style="stop-color:#A39F9F"/>
						<stop  offset="1" style="stop-color:#000000"/>
					</radialGradient>
					<circle fill="url(#XMLID_21_)" cx="223.538" cy="175.677" r="27.531"/>
				</g>
			</mask>
			<circle opacity="0.6" clip-path="url(#XMLID_19_)" mask="url(#XMLID_20_)" fill="#0683F4" cx="223.64" cy="176.399" r="24.86"/>
		</g>
		<path opacity="0.3" fill="#FFFFFF" d="M197.018,175.043c14.311-14.898,37.99-15.38,52.893-1.07
			c3.923,3.77,6.839,8.191,8.756,12.929c-1.903-5.109-4.954-9.885-9.149-13.913c-14.903-14.311-38.582-13.831-52.893,1.07
			c-10.542,10.978-13.049,26.716-7.685,39.964C184.09,200.983,186.739,185.75,197.018,175.043z"/>
	</g>
	<g>
		<path opacity="0.85" fill="#FBAC39" d="M141.12,115.978c-20.291,0-36.739,16.449-36.739,36.741
			c0,20.289,16.448,36.736,36.739,36.736c20.29,0,36.738-16.447,36.738-36.736C177.858,132.427,161.41,115.978,141.12,115.978z
			 M141.12,188.125c-19.558,0-35.41-15.851-35.41-35.406c0-19.559,15.853-35.412,35.41-35.412c19.556,0,35.408,15.853,35.408,35.412
			C176.528,172.274,160.675,188.125,141.12,188.125z"/>
		<g>
			<defs>
				<circle id="XMLID_32_" cx="141.119" cy="152.719" r="36.399"/>
			</defs>
			<use xlink:href="#XMLID_32_"  opacity="0.85" fill="#FAA939"/>
			<clipPath id="XMLID_22_">
				<use xlink:href="#XMLID_32_"  opacity="0.85"/>
			</clipPath>
			<defs>
				<filter id="Adobe_OpacityMaskFilter_6_" filterUnits="userSpaceOnUse" x="121.798" y="169.793" width="38.64" height="38.641">
					<feColorMatrix  type="matrix" values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"/>
				</filter>
			</defs>
			<mask maskUnits="userSpaceOnUse" x="121.798" y="169.793" width="38.64" height="38.641" id="XMLID_24_">
				<g clip-path="url(#XMLID_22_)" filter="url(#Adobe_OpacityMaskFilter_6_)">
					<radialGradient id="XMLID_25_" cx="139.8369" cy="189.2764" r="19.352" gradientUnits="userSpaceOnUse">
						<stop  offset="0" style="stop-color:#A39F9F"/>
						<stop  offset="1" style="stop-color:#000000"/>
					</radialGradient>
					<circle fill="url(#XMLID_25_)" cx="141.039" cy="188.555" r="21.394"/>
				</g>
			</mask>
			<path opacity="0.75" clip-path="url(#XMLID_22_)" mask="url(#XMLID_24_)" fill="#FFFA57" d="M160.438,189.115
				c0,10.672-8.648,19.318-19.318,19.318c-10.671,0-19.322-8.646-19.322-19.318c0-10.67,8.651-19.322,19.322-19.322
				C151.79,169.793,160.438,178.445,160.438,189.115z"/>
		</g>
		<g>
			<defs>
				<circle id="XMLID_36_" cx="141.119" cy="152.719" r="36.399"/>
			</defs>
			<clipPath id="XMLID_26_">
				<use xlink:href="#XMLID_36_" />
			</clipPath>
			<defs>
				<filter id="Adobe_OpacityMaskFilter_7_" filterUnits="userSpaceOnUse" x="116.93" y="105.715" width="48.381" height="48.38">
					<feColorMatrix  type="matrix" values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"/>
				</filter>
			</defs>
			<mask maskUnits="userSpaceOnUse" x="116.93" y="105.715" width="48.381" height="48.38" id="XMLID_27_">
				<g clip-path="url(#XMLID_26_)" filter="url(#Adobe_OpacityMaskFilter_7_)">
					<radialGradient id="XMLID_29_" cx="139.5137" cy="130.105" r="24.23" gradientUnits="userSpaceOnUse">
						<stop  offset="0" style="stop-color:#A39F9F"/>
						<stop  offset="1" style="stop-color:#000000"/>
					</radialGradient>
					<circle fill="url(#XMLID_29_)" cx="141.019" cy="129.202" r="26.787"/>
				</g>
			</mask>
			
				<circle opacity="0.75" clip-path="url(#XMLID_26_)" mask="url(#XMLID_27_)" fill="#FFFA57" cx="141.12" cy="129.903" r="24.191"/>
		</g>
		<path opacity="0.3" fill="#FFFFFF" d="M115.216,128.584c13.923-14.5,36.964-14.965,51.465-1.043
			c3.816,3.669,6.651,7.97,8.518,12.58c-1.85-4.974-4.821-9.615-8.9-13.537c-14.501-13.925-37.54-13.457-51.465,1.041
			c-10.257,10.682-12.697,25.993-7.478,38.888C102.637,153.825,105.213,139,115.216,128.584z"/>
	</g>
</g>
</svg>
