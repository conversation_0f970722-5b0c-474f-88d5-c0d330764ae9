<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Generator: Adobe Illustrator 12.0.1, SVG Export Plug-In . SVG Version: 6.00 Build 51448)  -->

<svg
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://creativecommons.org/ns#"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   version="1.1"
   id="Layer_1"
   width="278.95901"
   height="242.25999"
   viewBox="0 0 278.95901 242.25999"
   overflow="visible"
   enable-background="new 0 0 516 172"
   xml:space="preserve"
   inkscape:version="0.48.2 r9819"
   sodipodi:docname="Section-Triangle.svg"
   style="overflow:visible"><metadata
   id="metadata24"><rdf:RDF><cc:Work
       rdf:about=""><dc:format>image/svg+xml</dc:format><dc:type
         rdf:resource="http://purl.org/dc/dcmitype/StillImage" /></cc:Work></rdf:RDF></metadata><defs
   id="defs22" /><sodipodi:namedview
   pagecolor="#ffffff"
   bordercolor="#666666"
   borderopacity="1"
   objecttolerance="10"
   gridtolerance="10"
   guidetolerance="10"
   inkscape:pageopacity="0"
   inkscape:pageshadow="2"
   inkscape:window-width="1057"
   inkscape:window-height="838"
   id="namedview20"
   showgrid="false"
   fit-margin-top="0"
   fit-margin-left="0"
   fit-margin-right="0"
   fit-margin-bottom="0"
   inkscape:zoom="0.66860465"
   inkscape:cx="151.015"
   inkscape:cy="125.018"
   inkscape:window-x="2367"
   inkscape:window-y="198"
   inkscape:window-maximized="0"
   inkscape:current-layer="Layer_1" />
<g
   id="g3"
   transform="translate(-106.985,31.242)">
	<polygon
   points="180.168,83.879 246.634,-31.242 313.099,83.879 "
   id="polygon5"
   style="fill:#00c109" />
	<polygon
   points="180.168,91.186 246.634,206.308 313.099,91.186 "
   id="polygon7"
   style="fill:#0054a5" />
	<polygon
   points="106.985,210.793 173.45,95.67 239.917,210.793 "
   id="polygon9"
   style="fill:#f16422" />
	<linearGradient
   id="XMLID_2_"
   gradientUnits="userSpaceOnUse"
   x1="175.9131"
   y1="46.5923"
   x2="336.40131"
   y2="202.03419">
		<stop
   offset="0"
   style="stop-color:#FF5F06"
   id="stop12" />
		<stop
   offset="0.1061"
   style="stop-color:#F95507"
   id="stop14" />
		<stop
   offset="1"
   style="stop-color:#CC0212"
   id="stop16" />
	</linearGradient>
	<polygon
   points="253.015,211.018 319.479,95.896 385.944,211.018 "
   id="polygon18"
   style="fill:url(#XMLID_2_)" />
</g>
</svg>