/**************************************************************
 * 
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 * 
 *   http://www.apache.org/licenses/LICENSE-2.0
 * 
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 * 
 *************************************************************/

.doctitle {
	font-family: <PERSON>l, Helvetica, sans-serif;
	font-size: 14px;
	color: #FFFFFF;
	font-weight: bold;
}
.docdescription {
	font-family: Arial, Helvetica, sans-serif;
	font-size: 12px;
	font-style: italic;
	color: #FFFFFF;
}
.docauthor {
	font-family: Arial, Helvetica, sans-serif;
	font-size: 12px;
	font-style: italic;
	color: #FFFFFF;
}
.doccreationdate {
	font-family: Arial, Helvetica, sans-serif;
	font-size: 12px;
	color: #FFFF00;
}
.doclastchangeddate {
	font-family: Arial, Helvetica, sans-serif;
	font-size: 12px;
	color: #000066;
}
.docfilename {
	font-family: Arial, Helvetica, sans-serif;
	font-size: 12px;
	color: #FFFFFF;
}
.docfileformatinfo {
	font-family: Arial, Helvetica, sans-serif;
	font-size: 12px;
	font-style: italic;
	color: #FFFFFF;
}
.docnumberofpages {
	font-family: Arial, Helvetica, sans-serif;
	font-size: 12px;
	color: #FFFFFF;
}
.docsizeinkb {
	font-family: Arial, Helvetica, sans-serif;
	font-size: 12px;
	color: #FFFFFF;
}
body {
	background-color: #CCCCCC;
	background-image: url(images/background.gif);
}
.toctitle {
	font-family: Arial, Helvetica, sans-serif;
	font-size: 20px;
	font-style: normal;
	font-weight: bold;
	color: #FFFFFF;
	background-color: #003333;
}
.tcolor {
	background-color: #990000;
}
.ccolor {
	background-color: #003333;
}
a:link {
	font-family: Arial, Helvetica, sans-serif;
	color: #FFFF00;
}
a:visited {
	font-family: Arial, Helvetica, sans-serif;
	color: #0099FF;
}
a:active {
	font-family: Arial, Helvetica, sans-serif;
	color: #99FF00;
}
