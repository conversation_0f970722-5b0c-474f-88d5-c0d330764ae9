<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 12.0.1, SVG Export Plug-In . SVG Version: 6.00 Build 51448)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" [
	<!ENTITY ns_svg "http://www.w3.org/2000/svg">
	<!ENTITY ns_xlink "http://www.w3.org/1999/xlink">
]>
<svg  version="1.1" id="Layer_1" xmlns="&ns_svg;" xmlns:xlink="&ns_xlink;" width="179.992" height="259.058"
	 viewBox="0 0 179.992 259.058" overflow="visible" enable-background="new 0 0 179.992 259.058" xml:space="preserve">
<g>
	<g>
		<defs>
			<filter id="Adobe_OpacityMaskFilter" filterUnits="userSpaceOnUse" x="17.717" y="14.844" width="152.448" height="232.269">
				<feFlood  style="flood-color:white;flood-opacity:1" result="back"/>
				<feBlend  in="SourceGraphic" in2="back" mode="normal"/>
			</filter>
		</defs>
		<mask maskUnits="userSpaceOnUse" x="17.717" y="14.844" width="152.448" height="232.269" id="XMLID_4_">
			<g filter="url(#Adobe_OpacityMaskFilter)">
				
					<image width="157" height="237" xlink:href="data:image/jpeg;base64,/9j/4AAQSkZJRgABAgEASABIAAD/7AARRHVja3kAAQAEAAAAHgAA/+4AIUFkb2JlAGTAAAAAAQMA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" transform="matrix(1 0 0 1 15.4238 12.4414)">
				</image>
			</g>
		</mask>
		<g mask="url(#XMLID_4_)">
			<rect x="17.717" y="14.844" fill="#B2B2B2" width="152.448" height="232.269"/>
		</g>
	</g>
	<rect x="12.807" y="10.917" fill="#EFEFEF" width="152.45" height="232.27"/>
	<linearGradient id="XMLID_5_" gradientUnits="userSpaceOnUse" x1="89.0317" y1="8.1924" x2="89.0317" y2="55.1705">
		<stop  offset="0" style="stop-color:#00BC00"/>
		<stop  offset="1" style="stop-color:#029902"/>
	</linearGradient>
	<rect x="12.807" y="10.917" fill="url(#XMLID_5_)" width="152.45" height="45.799"/>
	<linearGradient id="XMLID_6_" gradientUnits="userSpaceOnUse" x1="89.0317" y1="238.8306" x2="89.0317" y2="243.4273">
		<stop  offset="0" style="stop-color:#00BC00"/>
		<stop  offset="1" style="stop-color:#029902"/>
	</linearGradient>
	<rect x="12.807" y="238.925" fill="url(#XMLID_6_)" width="152.45" height="3.926"/>
</g>
</svg>
