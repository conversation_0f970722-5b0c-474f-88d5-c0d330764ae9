<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE toolbar:toolbar PUBLIC "-//OpenOffice.org//DTD OfficeDocument 1.0//EN" "toolbar.dtd">
<!--***********************************************************
 * 
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 * 
 *   http://www.apache.org/licenses/LICENSE-2.0
 * 
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 * 
 ***********************************************************-->


<toolbar:toolbar xmlns:toolbar="http://openoffice.org/2001/toolbar" xmlns:xlink="http://www.w3.org/1999/xlink" toolbar:id="toolbar">
 <toolbar:toolbaritem xlink:href=".uno:SelectObject" toolbar:style="radio"/>
 <toolbar:toolbarseparator/>
 <toolbar:toolbaritem xlink:href=".uno:Line" toolbar:style="radio"/>
 <toolbar:toolbaritem xlink:href=".uno:LineArrowEnd" toolbar:style="radio"/>
 <toolbar:toolbaritem xlink:href=".uno:Rect" toolbar:style="radio"/>
 <toolbar:toolbaritem xlink:href=".uno:Ellipse" toolbar:style="radio"/>
 <toolbar:toolbaritem xlink:href=".uno:Text" toolbar:style="radio"/>
 <toolbar:toolbaritem xlink:href=".uno:VerticalText" toolbar:style="radio"/>
 <toolbar:toolbarseparator/>
 <toolbar:toolbaritem xlink:href=".uno:LineToolbox" toolbar:style="radio dropdown"/>
 <toolbar:toolbaritem xlink:href=".uno:ConnectorToolbox" toolbar:style="radio dropdown"/>
 <toolbar:toolbaritem xlink:href=".uno:ArrowsToolbox" toolbar:helpid=".uno:ArrowsToolbox" toolbar:style="radio dropdown"/>
 <toolbar:toolbaritem xlink:href=".uno:Objects3DToolbox" toolbar:style="radio dropdown" toolbar:visible="false"/>
 <toolbar:toolbaritem xlink:href=".uno:BasicShapes" toolbar:style="radio dropdown"/>
 <toolbar:toolbaritem xlink:href=".uno:SymbolShapes" toolbar:style="radio dropdown"/>
 <toolbar:toolbaritem xlink:href=".uno:ArrowShapes" toolbar:style="radio dropdown"/>
 <toolbar:toolbaritem xlink:href=".uno:FlowChartShapes" toolbar:style="radio dropdown"/>
 <toolbar:toolbaritem xlink:href=".uno:CalloutShapes" toolbar:style="radio dropdown"/>
 <toolbar:toolbaritem xlink:href=".uno:StarShapes" toolbar:style="radio dropdown"/>
 <toolbar:toolbarseparator/>
 <toolbar:toolbaritem xlink:href=".uno:ToggleObjectBezierMode"/>
 <toolbar:toolbaritem xlink:href=".uno:GlueEditMode" toolbar:style="auto"/>
 <toolbar:toolbaritem xlink:href=".uno:ChangeBezier" toolbar:visible="false"/>
 <toolbar:toolbaritem xlink:href=".uno:ChangePolygon" toolbar:visible="false"/>
 <toolbar:toolbaritem xlink:href=".uno:ConvertInto3D" toolbar:visible="false"/>
 <toolbar:toolbaritem xlink:href=".uno:ConvertInto3DLatheFast" toolbar:visible="false"/>
 <toolbar:toolbarseparator/>
 <toolbar:toolbaritem xlink:href=".uno:FontworkGalleryFloater"/>
 <toolbar:toolbaritem xlink:href=".uno:InsertGraphic"/>
 <toolbar:toolbaritem xlink:href=".uno:Gallery"/>
 <toolbar:toolbarseparator/>
 <toolbar:toolbaritem xlink:href=".uno:AdvancedMode" toolbar:style="radio dropdown"/>
 <toolbar:toolbaritem xlink:href=".uno:Mirror" toolbar:visible="false"/>
 <toolbar:toolbaritem xlink:href=".uno:TransformDialog" toolbar:visible="false"/>
 <toolbar:toolbaritem xlink:href=".uno:ObjectAlign" toolbar:style="dropdown"/>
 <toolbar:toolbaritem xlink:href=".uno:ObjectPosition" toolbar:style="dropdown"/>
 <toolbar:toolbarseparator/>
 <toolbar:toolbaritem xlink:href=".uno:InsertToolbox" toolbar:style="radio dropdown" toolbar:visible="false"/>
 <toolbar:toolbaritem xlink:href=".uno:Config" toolbar:style="dropdown" toolbar:visible="false"/>
 <toolbar:toolbarseparator/>
 <toolbar:toolbaritem xlink:href=".uno:ExtrusionToggle"/>
</toolbar:toolbar>