<?xml version="1.0" encoding="UTF-8"?>
<!--***********************************************************
 * 
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 * 
 *   http://www.apache.org/licenses/LICENSE-2.0
 * 
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 * 
 ***********************************************************-->


<!-- =================================
  
  This templates creates a source.xml file
  which is identicall to the source xml tree
  used for the transformation.
  This is may be usefull for deveopement/debuging
  of layouts.
  
  ==================================== -->

<xsl:stylesheet version="1.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
  xmlns:redirect="http://xml.apache.org/xalan/redirect" 
  extension-element-prefixes="redirect">

   <xsl:output method="xml"/>
    
 
   <xsl:template  match="/">
	 	    <xsl:apply-templates mode="copy"/>
   </xsl:template>
	 
   <xsl:template match="@*|node()" mode="copy">
      <xsl:copy>
	     <xsl:apply-templates select="@*|node()" mode="copy"/>
	   </xsl:copy>
   </xsl:template>    
    
</xsl:stylesheet>
